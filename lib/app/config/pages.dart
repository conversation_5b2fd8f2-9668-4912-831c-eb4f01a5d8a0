import 'package:get/get.dart';

import '../pages/profile/account_security_page.dart';
import '../pages/tabs/transfers/portability/portability_request_asset_selection_page.dart';
import '../pages/tabs/transfers/portability/portability_request_exchange_selection_page.dart';
import '../pages/tabs/transfers/portability_b3_page.dart';
import '../pages/tabs/transfers/portability_request_page.dart';
import '../pages/trade/analyst_trade_how_it_works_page.dart';
import '../pages/trade/analyst_trade_how_to_hire_page.dart';
import '../pages/trade/analyst_hiring_page.dart';
import '../pages/trade/analyst_history_page.dart';
import '../pages/trade/analyst_trade_minimum_balance_page.dart';
import '../pages/trade/analyst_list_page.dart';
import '../pages/auth/electronic_signature.dart';
import '../pages/auth/first_access/first_access_declaration_page.dart';
import '../pages/auth/first_access/first_access_final_page.dart';
import '../pages/auth/first_access/first_access_initial_page.dart';
import '../pages/auth/first_access/first_access_password_page.dart';
import '../pages/auth/first_access/first_access_signature_page.dart';
import '../pages/auth/login/confirmation_login_page.dart';
import '../pages/auth/login/continue_login_biometrics_page.dart';
import '../pages/auth/login/continue_login_password_page.dart';
import '../pages/auth/login/new_login_cpf_page.dart';
import '../pages/auth/login/new_login_password_page.dart';
import '../pages/auth/new_password_page.dart';
import '../pages/auth/onboarding_page.dart';
import '../pages/auth/password_recovery/password_recovery_page.dart';
import '../pages/auth/signup/signup_email_confirmation_page.dart';
import '../pages/auth/signup/signup_final/signup_step_account_personal_data_page.dart';
import '../pages/auth/signup/signup_final_page.dart';
import '../pages/auth/signup/signup_initial_page.dart';
import '../pages/auth/signup/signup_pending_information.dart';
import '../pages/auth/signup/signup_resumption_confirmation_page.dart';
import '../pages/auth/signup/signup_resumption_request_page.dart';
import '../pages/auth/signup/signup_step_account_part_1_page.dart';
import '../pages/auth/signup/signup_step_account_part_2_page.dart';
import '../pages/auth/signup/signup_step_document_capture_intro_page.dart';
import '../pages/auth/signup/signup_step_document_data_confirmation_page.dart';
import '../pages/auth/signup/signup_step_document_selection_page.dart';
import '../pages/auth/signup/signup_step_document_submission_method_page.dart';
import '../pages/auth/signup/signup_step_document_guardian_document_data_confirmation_page.dart';
import '../pages/auth/signup/signup_step_document_guardian_personal_data_page.dart';
import '../pages/auth/signup/signup_step_document_selfie_page.dart';
import '../pages/auth/signup/signup_step_document_underage_intro_page.dart';
import '../pages/auth/signup/signup_step_address_part_1_page.dart';
import '../pages/auth/signup/signup_step_address_part_2_page.dart';
import '../pages/auth/signup/signup_step_address_part_3_page.dart';
import '../pages/auth/signup/signup_step_professional_information_part_1_page.dart';
import '../pages/auth/signup/signup_step_professional_information_part_2_page.dart';
import '../pages/auth/signup/signup_step_professional_information_part_3_page.dart';
import '../pages/auth/signup/signup_step_professional_information_part_4_page.dart';
import '../pages/auth/signup/signup_step_declaration_part_1_page.dart';
import '../pages/auth/signup_renewal/signup_renewal_data_intro_page.dart';
import '../pages/auth/signup_renewal/signup_renewal_declaration_page.dart';
import '../pages/auth/signup_renewal/signup_renewal_professional_data_page.dart';
import '../pages/auth/signup_renewal/signup_renewal_suitability_intro_page.dart';
import '../pages/auth/token_verification.dart';
import '../pages/help_page.dart';
import '../pages/notifications/notification_detail_page.dart';
import '../pages/notifications/notification_list_page.dart';
import '../pages/notifications/notification_settings_page.dart';
import '../pages/profile/bank_account_register_page.dart';
import '../pages/profile/bank_account_signature_page.dart';
import '../pages/profile/bank_account_token_page.dart';
import '../pages/profile/bank_accounts_page.dart';
import '../pages/profile/delete_account/delete_account_confirm_page.dart';
import '../pages/profile/delete_account/delete_account_page.dart';
import '../pages/profile/my_account_page.dart';
import '../pages/profile/my_account_settings_page.dart';
import '../pages/profile/qualified_investor_page.dart';
import '../pages/profile/secret_change_page.dart';
import '../pages/profile/suitability_page.dart';
import '../pages/profile/suitability_quiz_page.dart';
import '../pages/profile/terms_page.dart';
import '../pages/trade/strategy_trade_how_to_hire_page.dart';
import '../pages/trade/analyst_description_page.dart';
import '../pages/trade/strategy_description_page.dart';
import '../pages/trade/strategy_hiring_page.dart';
import '../pages/trade/strategy_history_page.dart';
import '../pages/trade/strategy_list_page.dart';
import '../pages/trade/strategy_trade_how_it_works_page.dart';
import '../pages/trade/strategy_trade_minimum_balance_page.dart';
import '../pages/strategic_trade/strategic_Information_page.dart';
import '../pages/strategic_trade/strategic_list_page.dart';
import '../pages/strategic_trade/strategic_trade_how_it_works_page.dart';
import '../pages/tabs/bank-statements/bank_statements_and_reports_page.dart';
import '../pages/tabs/bank-statements/brokerage_notes_page.dart';
import '../pages/tabs/bank-statements/earnings_page.dart';
import '../pages/tabs/bank-statements/faq_page.dart';
import '../pages/tabs/bank-statements/guarantee_and_margin_management_page.dart';
import '../pages/tabs/bank-statements/income_reports_page.dart';
import '../pages/tabs/bank-statements/monthly_statements_page.dart';
import '../pages/tabs/bank-statements/unsuitability_history_page.dart';
import '../pages/tabs/current_account_page.dart';
import '../pages/tabs/educational/educational_cm_school_history_page.dart';
import '../pages/tabs/educational/educational_cm_school_page.dart';
import '../pages/tabs/educational/educational_ebooks_page.dart';
import '../pages/tabs/educational/educational_traders_coach_page.dart';
import '../pages/tabs/educational/educational_video_detail_page.dart';
import '../pages/tabs/educational/educational_videos_page.dart';
import '../pages/tabs/filter_by_period_page.dart';
import '../pages/tabs/home-menu/configure_menu_page.dart';
import '../pages/tabs/home-menu/history_menu_page.dart';
import '../pages/tabs/home-menu/my_investments_page.dart';
import '../pages/tabs/investments/investment_closed_public_offer_detail_page.dart';
import '../pages/tabs/investments/investment_filter_page.dart';
import '../pages/tabs/investments/investment_history_page.dart';
import '../pages/tabs/investments/investment_product_detail_page.dart';
import '../pages/tabs/investments/investment_product_list_page.dart';
import '../pages/tabs/investments/investment_product_list_sort_page.dart';
import '../pages/tabs/investments/investments_page.dart';
import '../pages/tabs/investments/purchase/fixed_income_purchase_detail_page.dart';
import '../pages/tabs/investments/purchase/investment_funds_purchase_detail_page.dart';
import '../pages/tabs/investments/purchase/private_credit_purchase_detail_page.dart';
import '../pages/tabs/investments/purchase/recommended_portfolio_purchase_detail_page.dart';
import '../pages/tabs/investments/purchase/recommended_portfolio_purchase_filter_page.dart';
import '../pages/tabs/investments/purchase/recommended_portfolio_purchase_list_page.dart';
import '../pages/tabs/investments/purchase/recommended_portfolio_purchase_service_information_page.dart';
import '../pages/tabs/investments/purchase/recommended_portfolio_purchase_signature_page.dart';
import '../pages/tabs/investments/purchase/treasury_purchase_detail_page.dart';
import '../pages/tabs/investments/recommendations_filter_page.dart';
import '../pages/tabs/investments/recommendations_page.dart';
import '../pages/tabs/investments/redeem/fixed_income_redeem_page.dart';
import '../pages/tabs/investments/redeem/investment_funds_redeem_page.dart';
import '../pages/tabs/investments/redeem/private_credit_early_redeem_page.dart';
import '../pages/tabs/investments/redeem/treasury_redeem_page.dart';
import '../pages/tabs/my_investments/market_value_disclaimer_page.dart';
import '../pages/tabs/my_investments/my_investments.dart';
import '../pages/tabs/my_investments/recommended_portfolio/recommended_portfolio_list_information_page.dart';
import '../pages/tabs/my_investments/recommended_portfolio/recommended_portfolio_position_detail_history_page.dart';
import '../pages/tabs/my_investments/recommended_portfolio/recommended_portfolio_position_detail_page.dart';
import '../pages/tabs/online-shop/online_shop_menu_page.dart';
import '../pages/tabs/online-shop/online_shop_module_information_page.dart';
import '../pages/tabs/online-shop/online_shop_platform_cancel_page.dart';
import '../pages/tabs/online-shop/online_shop_platform_hire_page.dart';
import '../pages/tabs/online-shop/online_shop_platform_history_page.dart';
import '../pages/tabs/online-shop/online_shop_platform_list_page.dart';
import '../pages/tabs/online-shop/online_shop_product_list_page.dart';
import '../pages/tabs/online-shop/online_shop_service_list_page.dart';
import '../pages/tabs/online-shop/online_shop_service_signature_page.dart';
import '../pages/tabs/patrimony/patrimony_page.dart';
import '../pages/tabs/rentability/rentability_detailed_product_page.dart';
import '../pages/tabs/rentability/rentability_information_page.dart';
import '../pages/tabs/submenu_page.dart';
import '../pages/tabs/tab_page.dart';
import '../pages/tabs/trader-arena/leverage_assets_page.dart';
import '../pages/tabs/trader-arena/limit_allocation.dart';
import '../pages/tabs/trader-arena/rlp_benefits_page.dart';
import '../pages/tabs/trader-arena/trader_arena_page.dart';
import '../pages/tabs/transfers/deposit_information_page.dart';
import '../pages/tabs/transfers/deposit_method_page.dart';
import '../pages/tabs/transfers/transfer_custody_detail_page.dart';
import '../pages/tabs/transfers/transfer_custody_history_page.dart';
import '../pages/tabs/transfers/transfer_custody_page.dart';
import '../pages/tabs/transfers/transfer_history_page.dart';
import '../pages/tabs/transfers/transfer_resources_page.dart';
import '../pages/terms/terms_page.dart';
import '../pages/profile/contact_data_update_page.dart';

final pages = [
  GetPage(name: OnboardingPage.routeName, page: () => const OnboardingPage()),
  GetPage(name: NewLoginCpfPage.routeName, page: () => const NewLoginCpfPage()),
  GetPage(name: NewLoginPasswordPage.routeName, page: () => const NewLoginPasswordPage()),
  GetPage(name: ContinueLoginPasswordPage.routeName, page: () => const ContinueLoginPasswordPage()),
  GetPage(name: ContinueLoginBiometricsPage.routeName, page: () => const ContinueLoginBiometricsPage()),
  GetPage(name: ConfirmationLoginPage.routeName, page: () => const ConfirmationLoginPage()),
  GetPage(name: PasswordRecoveryPage.routeName, page: () => const PasswordRecoveryPage()),

  GetPage(name: FirstAccessInitialPage.routeName, page: () => const FirstAccessInitialPage()),
  GetPage(name: FirstAccessPasswordPage.routeName, page: () => const FirstAccessPasswordPage()),
  GetPage(name: FirstAccessSignaturePage.routeName, page: () => const FirstAccessSignaturePage()),
  GetPage(name: FirstAccessDeclarationPage.routeName, page: () => const FirstAccessDeclarationPage()),
  GetPage(name: FirstAccessFinalPage.routeName, page: () => const FirstAccessFinalPage()),

  GetPage(name: QualifiedInvestorPage.routeName, page: () => const QualifiedInvestorPage()),
  GetPage(name: TabPage.routeName, page: () => const TabPage()),
  GetPage(name: MyAccountPage.routeName, page: () => const MyAccountPage()),
  GetPage(name: MyAccountSettingsPage.routeName, page: () => const MyAccountSettingsPage()),
  GetPage(name: ConfigureMenuPage.routeName, page: () => const ConfigureMenuPage()),
  GetPage(name: RentabilityDetailedProductPage.routeName, page: () => const RentabilityDetailedProductPage()),
  GetPage(name: RentabilityInformationPage.routeName, page: () => const RentabilityInformationPage()),
  GetPage(name: FilterByPeriodPage.routeName, page: () => const FilterByPeriodPage()),
  GetPage(name: BankStatementsAndReportsPage.routeName, page: () => const BankStatementsAndReportsPage()),
  GetPage(name: ElectronicSignaturePage.routeName, page: () => ElectronicSignaturePage()),
  GetPage(name: BrokerageNotesPage.routeName, page: () => const BrokerageNotesPage()),
  GetPage(name: MonthlyStatementsPage.routeName, page: () => const MonthlyStatementsPage()),
  GetPage(name: UnsuitabilityHistoryPage.routeName, page: () => const UnsuitabilityHistoryPage()),
  GetPage(name: GuaranteeAndMarginManagementPage.routeName, page: () => const GuaranteeAndMarginManagementPage()),
  GetPage(name: EarningsPage.routeName, page: () => EarningsPage()),
  GetPage(name: IncomeReportsPage.routeName, page: () => const IncomeReportsPage()),
  GetPage(name: RLPBenefitsPage.routeName, page: () => const RLPBenefitsPage()),
  GetPage(name: SuitabilityPage.routeName, page: () => const SuitabilityPage()),
  GetPage(name: SuitabilityQuizPage.routeName, page: () => SuitabilityQuizPage()),
  GetPage(name: NotificationListPage.routeName, page: () => const NotificationListPage()),
  GetPage(name: NotificationSettingsPage.routeName, page: () => const NotificationSettingsPage()),
  GetPage(name: NotificationDetailPage.routeName, page: () => const NotificationDetailPage()),
  GetPage(name: BankAccountsPage.routeName, page: () => const BankAccountsPage()),
  GetPage(name: BankAccountRegisterPage.routeName, page: () => const BankAccountRegisterPage()),
  GetPage(name: BankAccountTokenPage.routeName, page: () => const BankAccountTokenPage()),
  GetPage(name: BankAccountSignaturePage.routeName, page: () => const BankAccountSignaturePage()),
  GetPage(name: SubmenuPage.routeName, page: () => const SubmenuPage()),
  GetPage(name: EducationalVideosPage.routeName, page: () => const EducationalVideosPage()),
  GetPage(name: EducationalVideoDetailPage.routeName, page: () => EducationalVideoDetailPage()),
  GetPage(name: EducationalTradersCoachPage.routeName, page: () => const EducationalTradersCoachPage()),
  GetPage(name: EducationalEbooksPage.routeName, page: () => const EducationalEbooksPage()),
  GetPage(name: EducationalCmSchoolPage.routeName, page: () => const EducationalCmSchoolPage()),
  GetPage(name: EducationalCmSchoolHistoryPage.routeName, page: () => const EducationalCmSchoolHistoryPage()),
  GetPage(name: TraderArenaPage.routeName, page: () => const TraderArenaPage()),
  GetPage(name: LimitAllocation.routeName, page: () => const LimitAllocation()),
  GetPage(name: LeverageAssetsPage.routeName, page: () => const LeverageAssetsPage()),
  GetPage(name: SecretChangePage.routeName, page: () => SecretChangePage()),
  GetPage(name: TokenVerificationPage.routeName, page: () => TokenVerificationPage()),
  GetPage(name: TransferResourcesPage.routeName, page: () => const TransferResourcesPage()),
  GetPage(name: TransferHistoryPage.routeName, page: () => const TransferHistoryPage()),
  GetPage(name: TransferCustodyPage.routeName, page: () => const TransferCustodyPage()),
  GetPage(name: TransferCustodyHistoryPage.routeName, page: () => const TransferCustodyHistoryPage()),
  GetPage(name: TransferCustodyDetailPage.routeName, page: () => const TransferCustodyDetailPage()),
  GetPage(name: DepositInformationPage.routeName, page: () => const DepositInformationPage()),
  GetPage(name: DepositMethodPage.routeName, page: () => const DepositMethodPage()),
  GetPage(name: CurrentAccountPage.routeName, page: () => CurrentAccountPage()),
  GetPage(name: PatrimonyPage.routeName, page: () => const PatrimonyPage()),
  GetPage(name: OnlineShopMenuPage.routeName, page: () => const OnlineShopMenuPage()),
  GetPage(name: OnlineShopProductListPage.routeName, page: () => const OnlineShopProductListPage()),
  GetPage(name: OnlineShopPlatformListPage.routeName, page: () => const OnlineShopPlatformListPage()),
  GetPage(name: OnlineShopPlatformHistoryPage.routeName, page: () => OnlineShopPlatformHistoryPage()),
  GetPage(name: OnlineShopPlatformHirePage.routeName, page: () => const OnlineShopPlatformHirePage()),
  GetPage(name: OnlineShopPlatformCancelPage.routeName, page: () => const OnlineShopPlatformCancelPage()),
  GetPage(name: OnlineShopServiceListPage.routeName, page: () => const OnlineShopServiceListPage()),
  GetPage(name: OnlineShopServiceSignaturePage.routeName, page: () => const OnlineShopServiceSignaturePage()),
  GetPage(name: OnlineShopModuleInformationPage.routeName, page: () => const OnlineShopModuleInformationPage()),
  GetPage(name: ClosedPublicOfferDetailPage.routeName, page: () => const ClosedPublicOfferDetailPage()),
  GetPage(name: NewPasswordPage.routeName, page: () => const NewPasswordPage()),

  // Investir
  GetPage(name: InvestmentsPage.routeName, page: () => const InvestmentsPage()),
  GetPage(name: InvestmentProductListPage.routeName, page: () => const InvestmentProductListPage()),
  GetPage(name: InvestmentFilterPage.routeName, page: () => const InvestmentFilterPage()),
  GetPage(name: InvestmentProductListSortPage.routeName, page: () => const InvestmentProductListSortPage()),
  GetPage(name: InvestmentHistoryPage.routeName, page: () => const InvestmentHistoryPage()),
  GetPage(name: MyInvestmentsPage.routeName, page: () => const MyInvestmentsPage()),
  GetPage(name: HistoryMenuPage.routeName, page: () => const HistoryMenuPage()),
  GetPage(name: RecommendationsPage.routeName, page: () => const RecommendationsPage()),
  GetPage(name: RecommendationsFilterPage.routeName, page: () => const RecommendationsFilterPage()),

  GetPage(name: InvestmentProductDetailPage.routeName, page: () => const InvestmentProductDetailPage()),
  GetPage(name: RecommendedPortfolioPurchaseListPage.routeName, page: () => const RecommendedPortfolioPurchaseListPage()),
  GetPage(name: RecommendedPortfolioPurchaseFilterPage.routeName, page: () => const RecommendedPortfolioPurchaseFilterPage()),
  GetPage(name: RecommendedPortfolioPurchaseDetailPage.routeName, page: () => const RecommendedPortfolioPurchaseDetailPage()),
  GetPage(name: RecommendedPortfolioPurchaseServiceInformationPage.routeName, page: () => const RecommendedPortfolioPurchaseServiceInformationPage()),
  GetPage(name: RecommendedPortfolioPurchaseSignaturePage.routeName, page: () => const RecommendedPortfolioPurchaseSignaturePage()),
  GetPage(name: RecommendedPortfolioPositionDetailPage.routeName, page: () => const RecommendedPortfolioPositionDetailPage()),
  GetPage(name: RecommendedPortfolioPositionDetailHistoryPage.routeName, page: () => const RecommendedPortfolioPositionDetailHistoryPage()),
  GetPage(name: FixedIncomePurchaseDetailPage.routeName, page: () => const FixedIncomePurchaseDetailPage()),
  GetPage(name: TreasuryPurchaseDetailPage.routeName, page: () => const TreasuryPurchaseDetailPage()),
  GetPage(name: InvestmentFundsPurchaseDetailPage.routeName, page: () => const InvestmentFundsPurchaseDetailPage()),
  GetPage(name: PrivateCreditPurchaseDetailPage.routeName, page: () => const PrivateCreditPurchaseDetailPage()),

  // Resgate
  GetPage(name: InvestmentFundsRedeemPage.routeName, page: () => const InvestmentFundsRedeemPage()),
  GetPage(name: FixedIncomeRedeemPage.routeName, page: () => const FixedIncomeRedeemPage()),
  GetPage(name: TreasuryRedeemPage.routeName, page: () => const TreasuryRedeemPage()),

  // Meus investimentos
  GetPage(name: RecommendedPortfolioBalancePage.routeName, page: () => const RecommendedPortfolioBalancePage()),
  GetPage(name: RecommendedPortfolioListPage.routeName, page: () => const RecommendedPortfolioListPage()),
  GetPage(name: RecommendedPortfolioListPageInformationPage.routeName, page: () => const RecommendedPortfolioListPageInformationPage()),
  GetPage(name: FuturesMarketBalancePage.routeName, page: () => const FuturesMarketBalancePage()),
  GetPage(name: FuturesMarketAssetListPage.routeName, page: () => const FuturesMarketAssetListPage()),
  GetPage(name: SpotMarketBalancePage.routeName, page: () => const SpotMarketBalancePage()),
  GetPage(name: SpotMarketAssetListPage.routeName, page: () => const SpotMarketAssetListPage()),
  GetPage(name: InvestmentFundsBalancePage.routeName, page: () => const InvestmentFundsBalancePage()),
  GetPage(name: InvestmentFundDetailPage.routeName, page: () => const InvestmentFundDetailPage()),
  GetPage(name: InvestmentFundsListPage.routeName, page: () => const InvestmentFundsListPage()),
  GetPage(name: FixedIncomeBalancePage.routeName, page: () => const FixedIncomeBalancePage()),
  GetPage(name: FixedIncomeAssetDetailPage.routeName, page: () => const FixedIncomeAssetDetailPage()),
  GetPage(name: FixedIncomeAssetListPage.routeName, page: () => const FixedIncomeAssetListPage()),
  GetPage(name: TreasuryBalancePage.routeName, page: () => const TreasuryBalancePage()),
  GetPage(name: TreasuryReinvestmentPage.routeName, page: () => const TreasuryReinvestmentPage()),
  GetPage(name: TreasuryBondListPage.routeName, page: () => const TreasuryBondListPage()),
  GetPage(name: TreasuryBondDetailPage.routeName, page: () => const TreasuryBondDetailPage()),
  GetPage(name: TreasuryPendingTransactionDetailPage.routeName, page: () => const TreasuryPendingTransactionDetailPage()),
  GetPage(name: PrivateCreditBalancePage.routeName, page: () => const PrivateCreditBalancePage()),
  GetPage(name: PrivateCreditListPage.routeName, page: () => const PrivateCreditListPage()),
  GetPage(name: PrivateCreditDetailPage.routeName, page: () => const PrivateCreditDetailPage()),
  GetPage(name: PrivateCreditEarlyRedeemPage.routeName, page: () => const PrivateCreditEarlyRedeemPage()),
  GetPage(name: MarketValueDisclaimerPage.routeName, page: () => const MarketValueDisclaimerPage()),

  // Trade do Analista
  GetPage(name: AnalystMyListPage.routeName, page: () => const AnalystMyListPage()),
  GetPage(name: AnalystListPage.routeName, page: () => const AnalystListPage()),
  GetPage(name: AnalystTradeHowItWorksPage.routeName, page: () => const AnalystTradeHowItWorksPage()),
  GetPage(name: AnalystTradeHowToHirePage.routeName, page: () => const AnalystTradeHowToHirePage()),
  GetPage(name: AnalystTradeMinimumBalancePage.routeName, page: () => const AnalystTradeMinimumBalancePage()),
  GetPage(name: AnalystDescriptionPage.routeName, page: () => const AnalystDescriptionPage()),
  GetPage(name: AnalystHiringPage.routeName, page: () => const AnalystHiringPage()),
  GetPage(name: AnalystHistoryPage.routeName, page: () => const AnalystHistoryPage()),

  //Trade estrategico
  GetPage(name: StrategicListPage.routeName, page: () => const StrategicListPage()),
  GetPage(name: StrategicTradeHowItWorksPage.routeName, page: () => const StrategicTradeHowItWorksPage()),
  GetPage(name: StrategicInformationPage.routeName, page: () => const StrategicInformationPage()),

  // Trade Quantitativo
  GetPage(name: StrategyMyListPage.routeName, page: () => const StrategyMyListPage()),
  GetPage(name: StrategyListPage.routeName, page: () => const StrategyListPage()),
  GetPage(name: StrategyTradeHowItWorksPage.routeName, page: () => const StrategyTradeHowItWorksPage()),
  GetPage(name: StrategyTradeHowToHirePage.routeName, page: () => const StrategyTradeHowToHirePage()),
  GetPage(name: StrategyTradeMinimumBalancePage.routeName, page: () => const StrategyTradeMinimumBalancePage()),
  GetPage(name: StrategyDescriptionPage.routeName, page: () => const StrategyDescriptionPage()),
  GetPage(name: StrategyHiringPage.routeName, page: () => const StrategyHiringPage()),
  GetPage(name: StrategyHistoryPage.routeName, page: () => const StrategyHistoryPage()),

  GetPage(name: FaqPage.routeName, page: () => const FaqPage()),
  GetPage(name: AccountSecurityPage.routeName, page: () => const AccountSecurityPage()),
  GetPage(name: TermsPage.routeName, page: () => const TermsPage()),
  GetPage(name: TermsAndPrivacyPage.routeName, page: () => const TermsAndPrivacyPage()),
  GetPage(name: DeleteAccountPage.routeName, page: () => const DeleteAccountPage()),
  GetPage(name: HelpPage.routeName, page: () => const HelpPage()),
  GetPage(name: DeleteAccountConfirmPage.routeName, page: () => const DeleteAccountConfirmPage()),

  GetPage(name: SignupInitialPage.routeName, page: () => const SignupInitialPage()),
  GetPage(name: SignupStepAccountPart1Page.routeName, page: () => const SignupStepAccountPart1Page()),
  GetPage(name: SignupStepAccountPart2Page.routeName, page: () => const SignupStepAccountPart2Page()),
  GetPage(name: SignupStepDocumentUnderageIntroPage.routeName, page: () => const SignupStepDocumentUnderageIntroPage()),
  GetPage(name: SignupStepDocumentCaptureIntroPage.routeName, page: () => const SignupStepDocumentCaptureIntroPage()),
  GetPage(name: SignupStepDocumentSelectionPage.routeName, page: () => const SignupStepDocumentSelectionPage()),
  GetPage(name: SignupStepDocumentSubmissionMethodPage.routeName, page: () => const SignupStepDocumentSubmissionMethodPage()),
  GetPage(name: SignupStepDocumentUnderageDocumentSelectionMethodPage.routeName, page: () => const SignupStepDocumentUnderageDocumentSelectionMethodPage()),
  GetPage(name: SignupStepDocumentUnderageDocumentSubmissionMethodPage.routeName, page: () => const SignupStepDocumentUnderageDocumentSubmissionMethodPage()),
  GetPage(name: SignupStepDocumentDataConfirmationPage.routeName, page: () => const SignupStepDocumentDataConfirmationPage()),
  GetPage(name: SignupStepDocumentGuardianDocumentDataConfirmationPage.routeName, page: () => const SignupStepDocumentGuardianDocumentDataConfirmationPage()),
  GetPage(name: SignupStepDocumentGuardianPersonalDataPage.routeName, page: () => const SignupStepDocumentGuardianPersonalDataPage()),
  GetPage(name: SignupStepAccountPersonalDataPage.routeName, page: () => const SignupStepAccountPersonalDataPage()),
  GetPage(name: SignupStepDocumentSelfiePage.routeName, page: () => const SignupStepDocumentSelfiePage()),
  GetPage(name: SignupStepAddressPart1Page.routeName, page: () => const SignupStepAddressPart1Page()),
  GetPage(name: SignupStepAddressPart2Page.routeName, page: () => const SignupStepAddressPart2Page()),
  GetPage(name: SignupStepAddressPart3Page.routeName, page: () => const SignupStepAddressPart3Page()),
  GetPage(name: SignupStepProfessionalInformationPart1Page.routeName, page: () => const SignupStepProfessionalInformationPart1Page()),
  GetPage(name: SignupStepProfessionalInformationPart2Page.routeName, page: () => const SignupStepProfessionalInformationPart2Page()),
  GetPage(name: SignupStepProfessionalInformationPart3Page.routeName, page: () => const SignupStepProfessionalInformationPart3Page()),
  GetPage(name: SignupStepProfessionalInformationPart4Page.routeName, page: () => const SignupStepProfessionalInformationPart4Page()),
  GetPage(name: SignupStepDeclarationPart1Page.routeName, page: () => const SignupStepDeclarationPart1Page()),
  GetPage(name: SignupFinalPage.routeName, page: () => const SignupFinalPage()),
  GetPage(name: SignupResumptionRequestPage.routeName, page: () => const SignupResumptionRequestPage()),
  GetPage(name: SignupResumptionConfirmationPage.routeName, page: () => const SignupResumptionConfirmationPage()),

  GetPage(name: SignupRenewalSuitabilityIntroPage.routeName, page: () => const SignupRenewalSuitabilityIntroPage()),
  GetPage(name: SignupRenewalDeclarationPage.routeName, page: () => const SignupRenewalDeclarationPage()),
  GetPage(name: SignupRenewalDataIntroPage.routeName, page: () => const SignupRenewalDataIntroPage()),
  GetPage(name: SignupRenewalProfessionalDataPage.routeName, page: () => const SignupRenewalProfessionalDataPage()),
  GetPage(name: SignupEmailConfirmationPage.routeName, page: () => const SignupEmailConfirmationPage()),
  GetPage(name: SignupPendingInformation.routeName, page: () => const SignupPendingInformation()),
  GetPage(name: ContactDataUpdatePage.routeName, page: () => const ContactDataUpdatePage()),

  // Portabilidade
  GetPage(name: PortabilityRequestPage.routeName, page: () => const PortabilityRequestPage()),
  GetPage(name: PortabilityB3Page.routeName, page: () => const PortabilityB3Page()),
  GetPage(name: PortabilityRequestExchangeSelectionPage.routeName, page: () => const PortabilityRequestExchangeSelectionPage()),
  GetPage(name: PortabilityRequestAssetSelectionPage.routeName, page: () => const PortabilityRequestAssetSelectionPage()),
];
