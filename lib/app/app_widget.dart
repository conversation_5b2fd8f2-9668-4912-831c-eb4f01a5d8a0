import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_exit_app/flutter_exit_app.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:jailbreak_root_detection/jailbreak_root_detection.dart';

import 'api/shared/shared_api.dart';
import 'config/app_theme.dart';
import 'config/pages.dart';
import 'controllers/signup/signup_renewal_controller.dart';
import 'controllers/transfers/portability_history_controller.dart';
import 'pages/auth/forbidden_app_usage_page.dart';
import 'pages/auth/login/continue_login_biometrics_page.dart';
import 'pages/auth/login/continue_login_password_page.dart';
import 'pages/auth/onboarding_page.dart';
import 'pages/profile/my_account_settings_page.dart';
import 'pages/tabs/tab_page.dart';
import 'plugins/onesignal.dart';
import 'utils/integrations.dart';
import 'utils/local_storage.dart';
import 'utils/route_history_observer.dart';
import 'utils/ui_utils.dart';
import 'utils/url_handlers.dart';
import 'widgets/loading/loading.dart';
import 'widgets/modal/cm_bottom_sheet.dart';
import 'widgets/app_lifecycle.dart';

class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  Future<void> _checkForAppUpdates() async {
    final sharedApi = Get.find<SharedApi>();
    final versionStatus = await sharedApi.validateAppVersion();

    if (versionStatus == VersionStatus.mandatoryUpdate) {
      return CMBottomSheet.simple(
        title: 'Nova versão do app disponível!',
        description: 'Para prosseguir com o login, atualize seu app da CM Capital.\n\n'
            'Prezando por segurança e melhor experiência, alguns ajustes foram disponibilizados para facilitar a sua jornada de investimentos.',
        buttonText: 'ATUALIZAR AGORA',
        buttonOnPressed: openAppStorePage,
        canPop: false,
      ).show();
    } else if (versionStatus == VersionStatus.optionalUpdate) {
      return CMBottomSheet.choice(
        title: 'Nova versão do app disponível!',
        description: 'Atualize agora mesmo seu app da CM Capital.\n\n'
            'A nova versão foi disponibilizada a fim de melhorar a sua experiência e facilitar sua jornada de investimentos.',
        topButtonText: 'ATUALIZAR AGORA',
        topButtonOnPressed: openAppStorePage,
        bottomButtonText: 'ATUALIZAR DEPOIS',
        bottomButtonOnPressed: Get.back,
      ).show();
    }
  }

  Future<void> _checkIfPhoneIsJailbroken() async {
    var isAppUsageForbidden = true;
    try {
      if (Platform.isIOS) {
        isAppUsageForbidden = false;
      } else {
        isAppUsageForbidden = !(await JailbreakRootDetection.instance.isRealDevice) || await JailbreakRootDetection.instance.isJailBroken;
      }
    } on PlatformException {
      isAppUsageForbidden = false;
    }
    if (isAppUsageForbidden) Get.offAll(const ForbiddenAppUsagePage());
  }

  Future<void> _checkScheduledMaintenance() async {
    try {
      final isUnderMaintenance = await Get.find<SharedApi>().checkIfServerIsUnderMaintenance();
      if (isUnderMaintenance) {
        return CMBottomSheet.simple(
          topWidget: Image.asset('assets/icons/dialog/treasury_market_suspended.png'),
          title: 'Manutenção Programada',
          description:
              'Usuário(a), nosso sistema se encontra em manutenção para melhor atendê-lo(a). Pedimos desculpas pelo inconveniente. Fique atento(a) às comunicações por e-mail para informações quanto ao status do sistema.',
          buttonText: 'Voltar',
          buttonOnPressed: FlutterExitApp.exitApp,
          canPop: false,
        ).show();
      }
    } catch (_) {}
  }

  static Future<Widget> _getInitialPage() async {
    final storage = LocalStorage();

    final hasSavedUuid = await storage.containsKey(LocalStorageKeys.uuid);
    final hasSavedLoginCode = await storage.containsKey(LocalStorageKeys.loginCode);

    handleDeepLinking();

    if (hasSavedUuid && hasSavedLoginCode) return const ContinueLoginBiometricsPage();
    if (hasSavedLoginCode) return const ContinueLoginPasswordPage();
    return const OnboardingPage();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(AppTheme.systemTheme);

    return GestureDetector(
      onTap: hideKeyboard,
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'CM Capital',
        theme: AppTheme.themeData,
        localizationsDelegates: const <LocalizationsDelegate<Object>>[
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
        ],
        builder: (_, child) => AppLifecycle(
          child: child ?? const Loading(),
        ),
        home: FutureBuilder(
          future: _getInitialPage(),
          initialData: Container(color: AppTheme.blueColor, child: const Loading()),
          builder: (context, snapshot) {
            if (snapshot.data == null) {
              return const OnboardingPage();
            } else {
              return snapshot.data!;
            }
          },
        ),
        onReady: () {
          initializeOneSignal();
          _checkForAppUpdates();
          if (kReleaseMode) _checkIfPhoneIsJailbroken();
          _checkScheduledMaintenance();
        },
        routingCallback: (routing) {
          if (routing?.isBack == true) {
            // Remove controller de recadastro, quando não é mais utilizado
            if (routing?.current == TabPage.routeName || routing?.current == MyAccountSettingsPage.routeName) SignupRenewalController.deleteInstance();

            // Remove controller de portabilidade, quando não é mais utilizado
            if (routing?.current == TabPage.routeName) PortabilityHistoryController.deleteInstance();
          }

          // Esconde teclado
          hideKeyboard();
        },
        navigatorObservers: [routeHistoryObserver],
        getPages: pages,
      ),
    );
  }
}
