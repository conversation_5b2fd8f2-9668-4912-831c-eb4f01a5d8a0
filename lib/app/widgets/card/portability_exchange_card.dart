import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../config/app_theme.dart';

class PortabilityExchangeCard extends StatelessWidget {
  const PortabilityExchangeCard({
    super.key,
    this.name = 'CM Capital',
    this.isOrigin = false,
    this.direction = Axis.horizontal,
  }) : isCmCapital = name == 'CM Capital';

  final String name;
  final bool isCmCapital;
  final bool isOrigin;
  final Axis direction;

  String get _sourceText => isOrigin ? 'Origem' : 'Destino';
  bool get _isHorizontal => direction == Axis.horizontal;
  String get _description {
    if (_isHorizontal) return 'Corretora de ${_sourceText.toLowerCase()} selecionada';
    return _sourceText;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCmCapital ? null : AppTheme.blueColor6,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AppTheme.blueColor6, width: 1),
      ),
      child: Flex(
        direction: direction,
        mainAxisSize: _isHorizontal ? MainAxisSize.max : MainAxisSize.min,
        crossAxisAlignment: _isHorizontal ? CrossAxisAlignment.center : CrossAxisAlignment.start,
        children: [
          // Ícone
          isCmCapital
              ? SvgPicture.asset(
                  'assets/images/cm-capital-icon-transparent.svg',
                  colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                  height: 24,
                )
              : const Icon(
                  Icons.paid_outlined,
                  size: 24,
                  color: AppTheme.whiteColor,
                ),

          SizedBox(
            width: _isHorizontal ? 16 : null,
            height: _isHorizontal ? null : 16,
          ),

          // Name and description
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Descrição
                Text(_description, style: AppTheme.regular12Grey3),

                // Nome
                const SizedBox(height: 4),
                Text(name, style: AppTheme.bold14White),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
