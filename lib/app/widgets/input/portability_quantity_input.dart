import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../utils/extensions.dart';
import '../../utils/formatters.dart';

class PortabilityQuantityInput extends StatelessWidget {
  const PortabilityQuantityInput({
    super.key,
    this.focusNode,
    this.initialValue,
    this.onMaxPressed,
    this.enabled = true,
    this.onChanged,
    this.onLeave,
  });

  final FocusNode? focusNode;
  final double? initialValue;
  final bool enabled;
  final void Function()? onMaxPressed;
  final void Function(String value)? onChanged;
  final void Function()? onLeave;

  String? get _initialValue => enabled
      ? (initialValue ?? 0) > 0
          ? initialValue.asPtBrDecimal
          : null
      : '0';

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      key: UniqueKey(),
      focusNode: focusNode,
      initialValue: _initialValue,
      enabled: enabled,
      style: AppTheme.regular12White.copyWith(color: AppTheme.whiteColor.withValues(alpha: enabled ? 1 : 0.5)),
      textAlign: TextAlign.right,
      decoration: InputDecoration(
        border: const UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.whiteColor)),
        enabledBorder: const UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.whiteColor)),
        focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.orangeColor)),
        focusedErrorBorder: const UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.redColor4)),
        errorBorder: const UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.redColor4)),
        disabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: AppTheme.whiteColor.withValues(alpha: 0.5))),
        suffixIcon: FittedBox(
          fit: BoxFit.scaleDown,
          child: GestureDetector(
            onTap: onMaxPressed,
            child: Text('Máx', style: AppTheme.regular10Orange.copyWith(color: AppTheme.orangeColor.withValues(alpha: enabled ? 1 : 0.5))),
          ),
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: numberInputFormatter,
      scrollPadding: const EdgeInsets.only(top: 20, bottom: 40),
      onChanged: onChanged,
      onTapOutside: (_) => onLeave?.call(),
      onFieldSubmitted: (_) => onLeave?.call(),
    );
  }
}
