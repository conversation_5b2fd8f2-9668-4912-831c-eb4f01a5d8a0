import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/transfer/portability_asset.dart';
import '../../utils/extensions.dart';
import '../../utils/ui_utils.dart';
import '../input/cm_switch.dart';
import '../input/labeled_checkbox.dart';
import '../input/portability_quantity_input.dart';

const _sectionExpandingDuration = Duration(milliseconds: 300);
const _selectedQuantityWidth = 110.0;
const _quantityHeight = 50.0;

class AssetQuantitySelectionList extends StatefulWidget {
  const AssetQuantitySelectionList({
    super.key,
    required this.assets,
    this.onAssetChanged,
  });

  final List<PortabilityAsset> assets;

  /// Utilizado para informar quando algum item foi alterado, para que o estado possa ser atualizado.
  ///
  /// Não é preciso passar a lista de ativos, pois [assets] j<PERSON> <PERSON> atualizada automaticamente.
  final void Function()? onAssetChanged;

  @override
  State<AssetQuantitySelectionList> createState() => _AssetQuantitySelectionListState();
}

class _AssetQuantitySelectionListState extends State<AssetQuantitySelectionList> {
  final Map<String, List<PortabilityAsset>> _sections = {};
  var _expandedSectionIndex = 0;

  @override
  void initState() {
    super.initState();
    _getSections();
  }

  @override
  void dispose() {
    for (var section in _sections.entries) {
      for (var asset in section.value) {
        asset.focusNode?.dispose();
      }
    }
    super.dispose();
  }

  void _getSections() {
    final sections = <String, List<PortabilityAsset>>{};
    for (var asset in widget.assets) {
      asset.focusNode = _FocusNode();
      if (sections.containsKey(asset.type)) {
        sections[asset.type]!.add(asset);
      } else {
        if ((asset.type ?? '').isNotEmpty) sections[asset.type!] = [asset];
      }
    }
    final sortedEntries = sections.entries.sorted((entryA, entryB) => entryA.key.compareTo(entryB.key));
    _sections.addEntries(sortedEntries);
  }

  void _onSectionTap(index) {
    // Expande ou contrai seção correspondente ao [index]
    setState(() => _expandedSectionIndex = index != _expandedSectionIndex ? index : -1);
  }

  void _onAllSelected(String type, bool isSelected) {
    final assetsOfType = _sections[type]!;
    for (var asset in assetsOfType) {
      asset.isSelected = isSelected;
      asset.selectedQuantity = isSelected ? asset.availableQuantity : 0;
    }
    setState(() {});
    widget.onAssetChanged?.call();
  }

  void _onAssetSelected(PortabilityAsset asset, bool isSelected) {
    // Caso haja algum ativo selecionado sem quantidade informada, que não seja o selecionado, impede a seleção
    final invalidAsset = _getAssetWithInvalidQuantity();
    if (invalidAsset != null && invalidAsset.id != asset.id) return;

    // Seleciona/deseleciona ativo
    asset.isSelected = isSelected;
    if (isSelected) {
      setState(() {});
      // Atraso para focar o campo após estar habilitado
      Future.delayed(const Duration(milliseconds: 200), () => asset.focusNode?.requestFocus());
    } else {
      asset.selectedQuantity = 0;
      (asset.focusNode as _FocusNode).resetFocusCheck();
      setState(() {});
    }
    widget.onAssetChanged?.call();
  }

  void _onAssetMaxPressed(PortabilityAsset asset) {
    setState(() => asset.selectedQuantity = asset.availableQuantity);
    hideKeyboard();
    widget.onAssetChanged?.call();
  }

  void _onAssetQuantityChanged(PortabilityAsset asset, double quantity) {
    asset.selectedQuantity = quantity;
  }

  void _onLeaveAssetQuantity(PortabilityAsset asset) {
    if ((asset.selectedQuantity ?? 0) <= 0) {
      // Atraso para evitar que apareça brevemente erro no campo quando é desmarcado
      Future.delayed(const Duration(milliseconds: 100), () {
        setState(() {});
        // Atraso para aguardar teclado sumir, para poder mostrar novamente
        Future.delayed(const Duration(milliseconds: 200), () => asset.focusNode?.requestFocus());
      });

      // Atualiza quantitade para a máxima, caso ultrapasse
    } else if ((asset.selectedQuantity ?? 0) > (asset.availableQuantity ?? 0)) {
      setState(() => asset.selectedQuantity = asset.availableQuantity);

      // Atualiza tela para retirar mensagem de erro quando valor é permitido
    } else {
      setState(() {});
    }
    widget.onAssetChanged?.call();
  }

  /// Retorna o primeiro ativo selecionado com quantidade inválida.
  PortabilityAsset? _getAssetWithInvalidQuantity() {
    for (var entry in _sections.entries) {
      for (var asset in entry.value) {
        if (asset.isSelected && (asset.selectedQuantity ?? 0) <= 0) {
          // Isso faz com que o campo inválido seja focado novamente após tocar em outro lugar.
          hideKeyboard();
          return asset;
        }
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        _sections.length,
        (index) {
          final section = _sections.entries.elementAt(index);
          return _Section(
            name: section.key,
            assets: section.value,
            isExpanded: _expandedSectionIndex == index,
            onSectionTap: _getAssetWithInvalidQuantity() != null ? null : () => _onSectionTap(index),
            onAllSelected: (isSelected) => _onAllSelected.call(section.key, isSelected),
            onAssetSelected: _onAssetSelected,
            onAssetMaxPressed: _onAssetMaxPressed,
            onAssetQuantityChanged: _onAssetQuantityChanged,
            onLeaveAssetQuantity: _onLeaveAssetQuantity,
          );
        },
      ),
    );
  }
}

class _Section extends StatelessWidget {
  const _Section({
    required this.name,
    required this.assets,
    this.isExpanded = true,
    this.onSectionTap,
    this.onAllSelected,
    this.onAssetSelected,
    this.onAssetMaxPressed,
    this.onAssetQuantityChanged,
    this.onLeaveAssetQuantity,
  });

  final String name;
  final List<PortabilityAsset> assets;
  final bool isExpanded;
  final void Function()? onSectionTap;
  final void Function(bool isSelected)? onAllSelected;
  final void Function(PortabilityAsset asset, bool isSelected)? onAssetSelected;
  final void Function(PortabilityAsset asset)? onAssetMaxPressed;
  final void Function(PortabilityAsset asset, double quantity)? onAssetQuantityChanged;
  final void Function(PortabilityAsset asset)? onLeaveAssetQuantity;

  int get _totalSelectedAssets => assets.where((asset) => asset.isSelected).length;
  String get _totalSelectedAssetsText => '${_totalSelectedAssets <= 0 ? 'Nenhum' : _totalSelectedAssets} selecionado${_totalSelectedAssets > 1 ? 's' : ''}';

  bool get _isAllSelected => assets.every((asset) => asset.selectedQuantity == asset.availableQuantity);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Cabeçalho
        Material(
          color: Colors.transparent,
          child: InkWell(
            highlightColor: Colors.white10,
            splashColor: Colors.white10,
            borderRadius: BorderRadius.circular(4),
            onTap: onSectionTap,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
              child: Row(
                children: [
                  Expanded(child: Text(name, style: AppTheme.bold16White)),
                  Text(_totalSelectedAssetsText, style: AppTheme.regular12Grey2),
                  const SizedBox(width: 10),
                  AnimatedRotation(
                    turns: isExpanded ? 0 : 0.5,
                    duration: _sectionExpandingDuration,
                    child: const Icon(Icons.keyboard_arrow_up_sharp, size: 26, color: AppTheme.whiteColor),
                  )
                ],
              ),
            ),
          ),
        ),

        AnimatedCrossFade(
          crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: _sectionExpandingDuration,
          firstChild: Container(),
          secondChild: Column(
            children: [
              // Botão para seleção de todos
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                width: double.infinity,
                decoration: BoxDecoration(border: Border.all(color: AppTheme.blueColor7, width: 1), borderRadius: BorderRadius.circular(4)),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: CMSwitch(
                  isOn: _isAllSelected,
                  onChanged: onAllSelected,
                  text: 'Selecionar tudo de "$name"',
                  padding: EdgeInsets.zero,
                ),
              ),

              // Cabeçalho
              const Row(
                children: [
                  SizedBox(width: 42),
                  Expanded(child: Text('Ativos', style: AppTheme.regular12Orange)),
                  Text('Disponível', style: AppTheme.regular12Orange),
                  SizedBox(
                    width: _selectedQuantityWidth + 16,
                    child: Text('Selecionado', style: AppTheme.regular12Orange, textAlign: TextAlign.end),
                  ),
                  SizedBox(width: 10),
                ],
              ),

              // Lista de ativos
              const SizedBox(height: 8),
              ...assets.mapIndexed(
                (index, asset) {
                  return Container(
                    color: index.isEven ? const Color(0xFF052A45) : null,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                    child: _Item(
                      focusNode: asset.focusNode,
                      name: asset.name ?? '',
                      availableQuantity: asset.availableQuantity ?? 0,
                      selectedQuantity: asset.selectedQuantity,
                      isSelected: asset.isSelected,
                      onSelected: (isSelected) => onAssetSelected?.call(asset, isSelected),
                      onMaxPressed: () => onAssetMaxPressed?.call(asset),
                      onQuantityChanged: (quantity) => onAssetQuantityChanged?.call(asset, quantity),
                      onLeaveAssetQuantity: () => onLeaveAssetQuantity?.call(asset),
                    ),
                  );
                },
              ).toList(),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }
}

class _Item extends StatelessWidget {
  const _Item({
    this.focusNode,
    this.isSelected = false,
    this.onSelected,
    required this.name,
    required this.availableQuantity,
    required this.selectedQuantity,
    this.onMaxPressed,
    this.onQuantityChanged,
    this.onLeaveAssetQuantity,
  });

  final FocusNode? focusNode;
  final bool isSelected;
  final void Function(bool isSelected)? onSelected;
  final String name;
  final double availableQuantity;
  final double? selectedQuantity;
  final void Function()? onMaxPressed;
  final void Function(double quantity)? onQuantityChanged;
  final void Function()? onLeaveAssetQuantity;

  bool get _showError => (focusNode as _FocusNode).hasBeenFocused && isSelected && (selectedQuantity ?? 0) <= 0;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Checkbox e Ativo
        Expanded(
          child: LabeledCheckbox(
            value: isSelected,
            onChanged: onSelected,
            textLabel: name,
          ),
        ),

        // Quantidade
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Espaçamento para compensar erro
            if (_showError) const SizedBox(height: 15),

            // Quantidades
            Row(
              children: [
                // Quantidade disponível
                SizedBox(
                  height: _quantityHeight,
                  child: Center(child: Text(availableQuantity.asPtBrDecimal, style: AppTheme.regular12White)),
                ),

                // Quantidade selecionada
                Container(
                  margin: const EdgeInsets.only(left: 16),
                  width: _selectedQuantityWidth,
                  height: _quantityHeight,
                  alignment: Alignment.topCenter,
                  child: PortabilityQuantityInput(
                    focusNode: focusNode,
                    initialValue: selectedQuantity,
                    enabled: isSelected,
                    onMaxPressed: onMaxPressed,
                    onChanged: (value) => onQuantityChanged?.call(double.tryParse(value) ?? 0),
                    onLeave: onLeaveAssetQuantity,
                  ),
                ),
              ],
            ),

            // Erro
            if (_showError) ...[
              const SizedBox(height: 4),
              const Text('Não pode ser zero', style: AppTheme.regular11Red4),
            ],
          ],
        ),
      ],
    );
  }
}

class _FocusNode extends FocusNode {
  /// FocusNode customizado para permitir verificar se o campo já foi focado.
  _FocusNode() {
    addListener(_checkFocus);
  }

  bool _hasBeenFocused = false;
  bool get hasBeenFocused => _hasBeenFocused;
  void _checkFocus() => hasFocus ? _hasBeenFocused = true : null;
  void resetFocusCheck() => _hasBeenFocused = false;

  @override
  void dispose() {
    removeListener(_checkFocus);
    super.dispose();
  }
}
