import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Wrapper para bibliotecas de armazenamento local
class LocalStorage {
  final FlutterSecureStorage _instance = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  Future<void> write(LocalStorageKeys key, dynamic value) async {
    await _instance.write(key: key.name, value: '$value');
  }

  Future<T?> read<T>(LocalStorageKeys key) async {
    final string = await _instance.read(key: key.name);

    if ((string ?? '').isEmpty) return null;

    dynamic value;

    switch (T) {
      case int:
        value = int.tryParse(string!);
        break;
      case double:
        value = double.tryParse(string!);
        break;
      case bool:
        value = string == 'true';
        break;
      case String:
      default:
        value = string;
        break;
    }

    return value;
  }

  Future<void> writeList(LocalStorageKeys key, List list) async {
    final stringList = list.join(',');
    await _instance.write(key: key.name, value: stringList);
  }

  Future<List<T>?> readList<T>(LocalStorageKeys key) async {
    final stringList = await _instance.read(key: key.name);
    if (stringList == null) return null;

    final untypedList = stringList.split(',');

    final typedList = untypedList
        .map(
          (value) {
            switch (T) {
              case int:
                return int.tryParse(value);
              case double:
                return double.tryParse(value);
              case bool:
                return value == 'true';
              case String:
              default:
                return value;
            }
          },
        )
        .toList()
        .cast<T>();

    return typedList;
  }

  Future<void> delete(LocalStorageKeys key) async {
    await _instance.delete(key: key.name);
  }

  Future<void> deleteAll() async {
    await _instance.deleteAll();
  }

  Future<bool> containsKey(LocalStorageKeys key) async {
    // O método containsKey do flutter_secure_storage 6.0.0 sempre retorna true no iOS.
    final value = await _instance.read(key: key.name);
    return value != null;
  }

  Future<void> deleteSensitiveData() async {
    for (final key in LocalStorageKeys.sensitiveDataKeys) {
      delete(key);
    }
  }
}

// Seria positivo trocar os nomes dos valores `userLogin` para `loginCode` e `loginCode` para `userCode`.
// Porém, seria necessário fazer isso de uma forma que não quebrasse o login para os usuários que tem dados de login salvo.
enum LocalStorageKeys {
  uuid,

  /// Código utilizado para o login do usuário, podendo ser ou o sinacor (número da conta) ou o CPF.
  userLogin,
  userName,

  /// ID do usuário.
  loginCode,
  avatar,
  shownBiometricsReminderModal,
  cardColor,

  allHomeMenuButtons,
  selectedHomeMenuButtons,

  lastBalance;

  static const sensitiveDataKeys = [
    uuid,
    userLogin,
    userName,
    loginCode,
    avatar,
  ];
}
