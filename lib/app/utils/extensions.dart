import 'dart:math';

import 'package:diacritic/diacritic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../config/app_theme.dart';
import 'formatters.dart';
import 'route_history_observer.dart';

/// Classes e métodos que podem ser úteis em diversas páginas do app.

/// Insere um elemento T entre os elementos de uma lista.
///
/// Exemplo: intersperse('Z', ['A', 'B', 'C']) // ['A', 'Z', 'B', 'Z', 'C']
Iterable<T> intersperse<T>(T element, Iterable<T> iterable, {bool outer = false}) sync* {
  final iterator = iterable.iterator;
  if (outer) yield element;
  if (iterator.moveNext()) {
    yield iterator.current;
    while (iterator.moveNext()) {
      yield element;
      yield iterator.current;
    }
  }
  if (outer) yield element;
}

extension IterableExtensions<T> on Iterable<T> {
  /// Insere um elemento T entre os elementos de uma lista. Usável como método extensível de uma lista.
  ///
  /// Exemplo: ['A', 'B', 'C'].placeBetween('Z') // ['A', 'Z', 'B', 'Z', 'C']
  Iterable<T> placeBetween(T element, {bool outer = false}) {
    return intersperse(element, this, outer: outer);
  }

  T? getElementById(int? id) {
    if (id == null) return null;
    final resultList = where((dynamic element) => element?.id == id).toList();
    if (resultList.isEmpty) return null;
    return resultList[0];
  }
}

extension IntExtensions on int {
  String toMegaBytes() => '${(this / (1024 * 1024)).asPtBr2Digits()} MB';

  double widgetListHeight({required double itemHeight, double extraHeight = 0, double? minimumHeight, double? maximumHeight}) {
    final calculatedHeight = (this * itemHeight) + extraHeight;
    if (minimumHeight != null && calculatedHeight < minimumHeight) return minimumHeight;
    if (maximumHeight != null && calculatedHeight > maximumHeight) return maximumHeight;
    return calculatedHeight;
  }
}

extension NumExtensions on num? {
  num get finiteNumber => (this ?? 0.0).isInfinite ? 0.0 : this ?? 0.0;

  String asCurrency({int decimalDigits = 2}) => NumberFormat.currency(locale: 'pt_BR', name: 'R\$', decimalDigits: decimalDigits).format(finiteNumber);

  bool isBetween(num start, num end, [bool isInclusive = false]) => this == null
      ? false
      : (isInclusive)
          ? this! >= start && this! <= end
          : this! > start && this! < end;

  /// Retorna um valor em um formato compacto: 5,03 mi, 6 mil, 982, 45,67.
  String get compact => NumberFormat.compactCurrency(locale: 'pt-BR', name: '', decimalDigits: 0).format(finiteNumber);

  /// Retorna um valor no formato pt-BR com 0 até 8 casas decimais.
  ///
  /// Exemplos: 1234 -> "1.234", 1234.56 -> "1.234,56", 3.444455555 -> "3,44445556"
  String get asPtBrDecimal => NumberFormat('#,###.########', 'pt-BR').format(finiteNumber);

  String asPtBr2Digits() => NumberFormat("0.00", 'pt-BR').format(finiteNumber);

  double asDoublePtBr2Digits() => double.parse((finiteNumber).asPtBr2Digits().replaceAll(",", "."));

  /// Retorna uma string formatada que combina o número atual com a palavra
  /// "contrato" no singular ou "contratos" no plural, dependendo do valor.
  ///
  /// int numberOfContracts = 5;
  /// print(numberOfContracts.formattedContract()); // Saída: "5 contratos"
  String formattedContract() {
    return '$this ${(this ?? 0) <= 1 ? 'contrato' : 'contratos'}';
  }
}

extension DoubleExtensions<num> on double? {
  /// Arredonda um [double] para que fique com [decimalDigitsAmount] casas decimais.
  ///
  /// [decimalDigitsAmount] = 1: 4.918999 -> 4.9 ou 1.333333 -> 1.3
  ///
  /// [decimalDigitsAmount] = 2: 4.918999 -> 4.92 ou 1.333333 -> 1.33
  ///
  /// [decimalDigitsAmount] = 3: 4.918999 -> 4.919 ou 1.333333 -> 1.333
  double roundDecimalDigits({int decimalDigitsAmount = 2}) {
    final multiplier = pow(10, decimalDigitsAmount);
    return ((this ?? 0) * multiplier).round() / multiplier;
  }

  /// Formata um número entre 0 e 100 para porcentagem.
  ///
  /// [decimalDigits] define a quantidade de casas decimais.
  ///
  /// [showPlusSign] define se um + é exibido para valores positivos.
  String asPercentage({int decimalDigits = 2, bool showPlusSign = false}) {
    final formatter = NumberFormat.decimalPercentPattern(decimalDigits: decimalDigits, locale: 'pt-BR');
    final prefix = finiteNumber >= 0 && showPlusSign ? '+' : '';

    return '$prefix${formatter.format(finiteNumber / 100)}';
  }

  String toPointsString({int decimalDigits = 2, bool showPlusSign = true}) {
    final NumberFormat formatter = NumberFormat.decimalPattern('pt_BR');
    formatter.minimumFractionDigits = decimalDigits;
    formatter.maximumFractionDigits = decimalDigits;
    final prefix = finiteNumber >= 0 && showPlusSign ? '+' : '';

    return '$prefix${formatter.format(this)} pts';
  }
}

extension StringExtensions on String {
  /// Método intencionado para ser usado em urls de páginas.
  ///
  /// Substitui um parâmetro `id` em uma rota por um valor específico.
  String withId(dynamic id) => replaceFirst(':id', '$id');
}

extension NullableStringExtensions on String? {
  String get firstWord => this?.split(" ").first ?? "";
  String get lastWord => this?.split(" ").last ?? "";
  String get firstCharacter => this?.split("").first ?? "";
  String get lastCharacter => this?.split("").last ?? "";

  String get initials {
    final words = (this ?? '').trim().split(' ');
    if (words.length < 2) return firstCharacter;
    return words.map((name) => name.firstCharacter).join().substring(0, 2);
  }

  Text asWidget({TextStyle? style}) => Text(
        this ?? '',
        style: style ?? AppTheme.defaultText,
        overflow: TextOverflow.fade,
      );

  bool matchToSearch(String? search) {
    if ((this ?? '').isEmpty || (search ?? '').isEmpty) return true;
    final valueToMatch = removeDiacritics(this!.trim().toLowerCase());
    final searchToMatch = removeDiacritics(search!.trim().toLowerCase());
    return valueToMatch.contains(searchToMatch);
  }

  String get maskedCpf {
    if (this == null || this!.length != 11 || !GetUtils.isNumericOnly(this!)) return '';
    return '${this!.substring(0, 3)}.${this!.substring(3, 6)}.${this!.substring(6, 9)}-${this!.substring(9, 11)}';
  }

  String get maskedCnpj {
    if (this == null || this!.length != 14 || !GetUtils.isNumericOnly(this!)) return '';
    return '${this!.substring(0, 2)}.${this!.substring(2, 5)}.${this!.substring(5, 8)}/${this!.substring(8, 12)}-${this!.substring(12, 14)}';
  }

  /// Remove "55" do início de uma `String` que representa um número de telefone, caso houver.
  String get brazilianPhoneCodeRemoved => (this ?? '').startsWith('55') ? this!.substring(2) : this ?? '';

  String get maskedPhoneNumber {
    if ((this ?? '').length != 11 && (this ?? '').length != 10 && !GetUtils.isNumericOnly(this ?? '')) return this ?? '';
    final textEditingValue = TextEditingValue(text: this ?? '');
    final formattedValue = PhoneNumberInputFormatter().formatEditUpdate(textEditingValue, textEditingValue);
    return formattedValue.text;
  }

  String get maskedZipCode {
    if ((this ?? '').length != 8 && !GetUtils.isNumericOnly(this ?? '')) return this ?? '';
    return '${this!.substring(0, 5)}-${this!.substring(5, 8)}';
  }

  DateTime? toDateTime() => (this ?? '').isEmpty ? null : DateFormat('dd/MM/yyyy').parse(this!);

  /// Retorna o código de um vídeo do YouTube a partir de uma URL.
  String get youtubeVideoCode {
    if (this == null) return '';
    final uri = Uri.tryParse(this!);
    if (uri == null) return '';
    if (uri.host.contains('youtube.com')) {
      return uri.queryParameters['v'] ?? '';
    } else if (uri.host.contains('youtu.be')) {
      return uri.pathSegments.isNotEmpty ? uri.pathSegments[0] : '';
    }
    return '';
  }
}

extension DateExtensions<num> on DateTime {
  String toddMMyyyy() => DateFormat('dd/MM/yyyy').format(this);

  String toddMMyyyyHHmm() => DateFormat('dd/MM/yyyy HH:mm').format(this);

  DateTime addMonthsAndYears({int months = 0, int years = 0}) {
    return DateTime(year + years, month + months, day, hour, minute, second, millisecond, microsecond);
  }

  String toMonthAndYear() {
    final month = DateFormat.MMMM('pt_BR').format(this).capitalize;

    return '$month/$year';
  }
}

extension NullableDateExtensions<num> on DateTime? {
  String? toddMMyyyy() => this?.toddMMyyyy();

  String? toddMMyyyyHHmm() => this?.toddMMyyyyHHmm();

  bool isBetween(DateTime start, DateTime end) => this == null ? false : this!.isAfter(start.subtract(const Duration(milliseconds: 1))) && this!.isBefore(end);

  DateTime? addMonthsAndYears({int months = 0, int years = 0}) => this?.addMonthsAndYears(months: months, years: years);

  bool get isUnderage => this?.isAfter(DateTime.now().addMonthsAndYears(years: -18)) ?? false;

  String? toMonthAndYear() => this?.toMonthAndYear();
}

extension MapIntStringExtensions on Map<int, String> {
  List<DropdownMenuItem<int>> get dropdownItems {
    return keys.map((key) => DropdownMenuItem(value: key, child: Text(this[key] ?? ''))).toList();
  }
}

extension ColorsExtensions on Color {
  static Color getRandomColor() {
    final random = Random();
    final red = random.nextInt(256);
    final green = random.nextInt(256);
    final blue = random.nextInt(256);
    return Color.fromARGB(255, red, green, blue);
  }

  MaterialColor toMaterialColor() {
    final Map<int, Color> shades = {
      50: Color.fromRGBO(red, green, blue, .1),
      100: Color.fromRGBO(red, green, blue, .2),
      200: Color.fromRGBO(red, green, blue, .3),
      300: Color.fromRGBO(red, green, blue, .4),
      400: Color.fromRGBO(red, green, blue, .5),
      500: Color.fromRGBO(red, green, blue, .6),
      600: Color.fromRGBO(red, green, blue, .7),
      700: Color.fromRGBO(red, green, blue, .8),
      800: Color.fromRGBO(red, green, blue, .9),
      900: Color.fromRGBO(red, green, blue, 1),
    };

    return MaterialColor(value, shades);
  }
}

extension EdgeInsetsExtensions on EdgeInsets {
  EdgeInsets get onlyVertical => EdgeInsets.only(top: top, bottom: bottom);

  EdgeInsets get onlyHorizontal => EdgeInsets.only(left: left, right: right);
}

extension TabControllerExtension on TabController {
  void navigateToTabQueryParam() {
    final tabIndex = int.tryParse(Get.parameters['tab'] ?? '');

    if (tabIndex != null && tabIndex >= 0 && tabIndex < length) {
      animateTo(tabIndex);
    }
  }
}

extension GetNavigationExtension on GetInterface {
  /// Remove uma rota da pilha de navegação.
  void popNamedExistent(String page) {
    final isPageInHistory = routeHistoryObserver.routeNames.contains(page);

    if (!isPageInHistory) return;

    final routeToRemove = routeHistoryObserver.history.firstWhere(
      (route) => route.settings.name == page,
    );

    Get.removeRoute(routeToRemove);
  }

  /// Navega para [page] e remove da pilha uma ocorrência anterior.
  /// Útil para quando existir referência circular entre páginas.
  ///
  /// Por exemplo, ao navegar de A para B e de B para A, ao invés de acumular
  /// as duas páginas na pilha, é possível remover a primeira ocorrência de A.
  Future<T?>? toNamedAndPopExistent<T>(
    String page, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    popNamedExistent(page);

    return Get.toNamed(
      page,
      arguments: arguments,
      id: id,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
    );
  }

  /// Substitui a rota atual por [page] e remove da pilha uma ocorrência anterior.
  /// Útil para quando existir referência circular entre páginas.
  ///
  /// Por exemplo, ao navegar de A para B e de B para A, ao invés de acumular
  /// as duas páginas na pilha, é possível remover a primeira ocorrência de A.
  Future<T?>? offNamedAndPopExistent<T>(
    String page, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    popNamedExistent(page);

    return Get.offNamed(
      page,
      arguments: arguments,
      id: id,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
    );
  }
}
