import 'package:json_annotation/json_annotation.dart';

part 'usuario_alterar_assinatura_request.g.dart';

@JsonSerializable()
class UsuarioAlterarAssinaturaRequest {
  @JsonKey(name: 'assinatura_nova')
  final String newSignature;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'senha_atual')
  final List<List<int>> password;

  final String token;

  UsuarioAlterarAssinaturaRequest({
    required this.password,
    required this.newSignature,
    required this.token,
  });

  factory UsuarioAlterarAssinaturaRequest.fromJson(Map<String, dynamic> json) => _$UsuarioAlterarAssinaturaRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioAlterarAssinaturaRequestToJson(this);
}
