import 'package:json_annotation/json_annotation.dart';

part 'usuario_login_v2_request.g.dart';

@JsonSerializable()
class UsuarioLoginV2Request {
  final String login;
  final List<List<int>> senha;
  final int validador;
  final String valor;

  UsuarioLoginV2Request({
    required this.login,
    required this.senha,
    required this.validador,
    required this.valor,
  });

  factory UsuarioLoginV2Request.fromJson(Map<String, dynamic> json) => _$UsuarioLoginV2RequestFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioLoginV2RequestToJson(this);
}
