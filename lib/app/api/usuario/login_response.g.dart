// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      token: json['token'] as String,
      user: Usuario.fromJson(json['usuario'] as Map<String, dynamic>),
      tokenExpirationDate: DateTime.parse(json['expiredate'] as String),
      hasAgreedToCustodyTerms: json['termo'] as bool,
      hasAgreedToRulesAndParameters: json['termorpa'] as bool,
      rulesAndParametersUrl: json['termorpalink'] as String,
      userPendingInformation: json['pendencias'] == null
          ? null
          : UserPendingInformation.fromJson(
              json['pendencias'] as Map<String, dynamic>),
    );

UserPendingInformation _$UserPendingInformationFromJson(
        Map<String, dynamic> json) =>
    UserPendingInformation(
      spouseCpf: json['cpfconjuge'] as bool? ?? false,
      companyCnpj: json['cnpjempresa'] as bool? ?? false,
    );
