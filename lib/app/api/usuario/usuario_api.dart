import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;

import '../../api/cm_request.dart';
import '../../api/signup/signup_options.dart';

import '../../controllers/user_controller.dart';
import '../../models/user_contact_data.dart';
import '../shared/sinacor_request.dart';
import 'user_classification.dart';
import 'user_renew_password.dart';
import 'usuario_dados.dart';
import 'usuario_login_request.dart';

import 'usuario_login_v2_request.dart';

import 'login_response.dart';
export 'login_response.dart';

import 'usuario_login_response.dart';

import 'usuario_cpf_response.dart';
export 'usuario_cpf_response.dart';

import 'usuario_senha_request.dart';

import 'usuario_primeiro_acesso_liberar_request.dart';
export 'usuario_primeiro_acesso_liberar_request.dart';

import 'usuario_primeiro_acesso_validar_token_request.dart';
export 'usuario_primeiro_acesso_validar_token_request.dart';

import 'usuario_senha_response.dart';

import 'usuario_validar_request.dart';

import 'usuario_validar_response.dart';

import 'usuario_redefinir_senha.dart';

import 'usuario_alterar_assinatura_request.dart';
export 'usuario_alterar_assinatura_request.dart';

import 'usuario_alterar_assinatura_validar_request.dart';
export 'usuario_alterar_assinatura_validar_request.dart';

import 'usuario_alterar_senha_request.dart';
export 'usuario_alterar_senha_request.dart';

import 'usuario_alterar_senha_validar_request.dart';
export 'usuario_alterar_senha_validar_request.dart';

import 'pessoa_verificar_documento_response.dart';
export 'pessoa_verificar_documento_response.dart';

import 'usuario_device_acessar_request.dart';

import 'usuario_device_atualizar_request.dart';

import 'usuario_device_registrar_request.dart';
export 'usuario_device_registrar_request.dart';

import 'usuario_device_verificar_request.dart';

import 'usuario_device_verificar_response.dart';

import 'usuario_conta_response.dart';
export 'usuario_conta_response.dart';

import 'usuario_codigo_digito_response.dart';
export 'usuario_codigo_digito_response.dart';

export 'ocultar_notificacao_cliente_request.dart';

class UsuarioApi {
  final _request = Get.find<CMRequest>();
  final _userController = Get.find<UserController>();

  Future<UsuarioLoginResponse> usuarioLogin(UsuarioLoginRequest request) async {
    final response = await _request.post('/UsuarioLogin', request.toJson());
    return UsuarioLoginResponse.fromJson(response.data);
  }

  Future<LoginResponse> usuarioLoginV2(UsuarioLoginV2Request request) async {
    final response = await _request.post(
      '/UsuarioLoginV2',
      request.toJson(),
      encryptionOptions: const EncryptionOptions(onlyEncryptValues: true),
    );
    return LoginResponse.fromJson(response.data);
  }

  Future<void> usuarioLogout() async {
    await _request.get('/UsuarioLogout');
  }

  Future<UsuarioCPFResponse> usuarioCPF() async {
    final response = await _request.get('/UsuarioCPF');
    final cpfResponse = UsuarioCPFResponse.fromJson(response.data);
    return UsuarioCPFResponse(cpf: cpfResponse.cpf.padLeft(11, '0'));
  }

  Future<UsuarioSenhaResponse> usuarioSenha(UsuarioSenhaRequest request) async {
    final response = await _request.post('/UsuarioSenha', request.toJson());
    return UsuarioSenhaResponse.fromJson(response.data);
  }

  Future<UsuarioValidarResponse> usuarioValidar(UsuarioValidarRequest request) async {
    final response = await _request.post('/UsuarioValidar', request.toJson());
    final userResponse = UsuarioValidarResponse.fromJson(response.data);
    _request.token = userResponse.token;

    return userResponse;
  }

  Future<UsuarioDados> usuarioDados() async {
    final response = await _request.post('/ClienteDados', SinacorRequest(convertToString: true).toJson());

    return UsuarioDados.fromJson(response.data);
  }

  Future<void> gravarClassificacao(UserClassification classification) async {
    final body = {
      "clienteCodigo": SinacorRequest(convertToString: true).sinacor,
      "classificacaoInvestidorId": classification.id,
    };

    await _request.post('/ClienteDados/gravarClassificacao', body);
  }

  Future<void> usuarioValidarTermosV2() async {
    await _request.post('/UsuarioValidarTermoV2', {'termo': 1});
  }

  Future<void> acceptRulesAndParameters() {
    return _request.post('/UsuarioValidarTermo/GravarTermo', {'id': 10});
  }

  Future<void> usuarioAlterarSenhaValidar(UsuarioAlterarSenhaValidarRequest request) async {
    await _request.post('/UsuarioAlterarSenhaValidar', request.toJson());
  }

  Future<void> usuarioAlterarSenha(UsuarioAlterarSenhaRequest request) async {
    await _request.post('/UsuarioAlterarSenha', request.toJson());
  }

  Future<void> usuarioAlterarAssinaturaValidar(UsuarioAlterarAssinaturaValidarRequest request) async {
    await _request.post('/UsuarioAlterarAssinaturaValidar', request.toJson());
  }

  Future<void> usuarioAlterarAssinatura(UsuarioAlterarAssinaturaRequest request) async {
    await _request.post('/UsuarioAlterarAssinatura', request.toJson());
  }

  Future<bool> usuarioVerificaPrimeiroAcesso(String login) async {
    final response = await _request.post(
      '/UsuarioLoginV2/validaPrimeiroAcesso',
      {'login': login},
      encryptionOptions: const EncryptionOptions(onlyEncryptValues: true),
    );
    return response.data is Map && response.data['primeiro_acesso'] == true;
  }

  Future<void> usuarioPrimeiroAcessoEmail(String login) async {
    await _request.post(
      '/UsuarioPrimeiroAcessoApp/email',
      {'login': login},
      encryptionOptions: const EncryptionOptions(onlyEncryptValues: true),
    );
  }

  Future<List<SignupDiscoverySource>?> usuarioPrimeiroComoConheceuCM(String login) async {
    final response = await _request.post(
      '/UsuarioPrimeiroAcessoApp/ListarComoConheceuCM',
      {'login': login},
      encryptionOptions: const EncryptionOptions(onlyEncryptValues: true),
    );

    return (response.data as List).map((option) => SignupDiscoverySource.fromJson(option)).toList();
  }

  Future<void> usuarioPrimeiroAcessoValidarToken(UsuarioPrimeiroAcessoValidarTokenRequest request) async {
    await _request.post(
      '/UsuarioPrimeiroAcessoApp/validarToken',
      request.toJson(),
      encryptionOptions: const EncryptionOptions(onlyEncryptValues: true),
    );
  }

  Future<void> usuarioPrimeiroAcessoLiberarAcesso(UsuarioPrimeiroAcessoLiberarRequest request) async {
    await _request.post(
      '/UsuarioPrimeiroAcessoApp/liberarAcesso',
      request.toJson(),
      encryptionOptions: const EncryptionOptions(
        onlyEncryptValues: true,
        valuesToEncrypt: UsuarioPrimeiroAcessoLiberarRequest.valuesToEncrypt,
      ),
    );
  }

  Future<void> usuarioResetarSenha(UsuarioRedefinirSenha request) async {
    await _request.post('/UsuarioResetarSenhaEnviar', request.toJson());
  }

  Future<void> usuarioCreateNewPassword(UserRenewPassword request, String token) async {
    _request.token = token;
    await _request.post('/UsuarioResetarSenha', request.toJson());
  }

  // Biometria – Registrar Dispositivo
  // Quando um Dispositivo é registrado, há necessidade da criação de um GUID.
  Future<void> usuarioDeviceRegistrar(UsuarioDeviceRegistrarRequest request) async {
    await _request.post('/UsuarioDeviceRegistrar', request.toJson());
  }

  // Biometria – Atualizar Dispositivo
  Future<void> usuarioDeviceAtualizar(UsuarioDeviceAtualizarRequest request) async {
    await _request.post('/UsuarioDeviceAtualizar', request.toJson(), options: Options(sendTimeout: const Duration(seconds: 40), receiveTimeout: const Duration(seconds: 40)));
  }

  // Biometria - Verificar documento
  // Com o envio do código de Sinacor do cliente é possível ver se ele existe.
  // Em caso de sucesso, obtemos o documento que foi registrado pelo cliente, o RG (Registro Geral) ou a CNH (Carteira Nacional de Habilitação).
  // Com essa informação solicito para que o cliente digite os 5 primeiros caracteres do documento(RG ou CNH).
  Future<PessoaVerificarDocumentoResponse> pessoaVerificarDocumento() async {
    final response = await _request.post('/PessoaVerificarDocumento', SinacorRequest(convertToString: true).toJson());
    return PessoaVerificarDocumentoResponse.fromJson(response.data);
  }

  // Biometria – Usuário Acessar
  // Não há necessidade de validação pois é o token final.
  Future<LoginResponse> usuarioDeviceAcessar(UsuarioDeviceAcessarRequest request) async {
    final response = await _request.post('/UsuarioDeviceAcessar', request.toJson());
    return LoginResponse.fromJson(response.data);
  }

  // Biometria - Verificação do Usuário
  // existe_usuario: Verifica se existe usuário no banco de dados.
  // existe_guid: Verifica se existe alguma GUID no banco de dados.
  // atual_guid: Verifica se a GUID passada é a mesma que se tem no banco de dados
  Future<UsuarioDeviceVerificarResponse> usuarioDeviceVerificar(UsuarioDeviceVerificarRequest request) async {
    final response = await _request.post('/UsuarioDeviceVerificar', request.toJson());
    return UsuarioDeviceVerificarResponse.fromJson(response.data);
  }

  /// Utilizado no login, após confirmação, apenas para salvar as contas.
  Future<List<Conta>> usuarioConta() async {
    final response = await _request.get('/UsuarioConta');
    final accounts = (response.data as List).map((conta) => Conta.fromJson(conta)).toList();
    if (accounts.isNotEmpty) {
      _userController.accounts = accounts;
      _userController.currentAccount = accounts[0];
    }

    return accounts;
  }

  Future<UsuarioCodigoDigitoResponse> usuarioCodigoDigito() async {
    final codigo = _userController.currentAccount?.account;
    final response = await _request.get('/UsuarioCodigoDigito?codigo=$codigo');
    // Os dados são disponibilizados como um array.
    final data = (response.data as List).map((codigoDigito) => UsuarioCodigoDigitoResponse.fromJson(codigoDigito)).toList();
    return data.isNotEmpty ? data.first : UsuarioCodigoDigitoResponse(codigo: 0, digito: 0);
  }

  /// Solitica encerramento da conta
  Future<void> solicitacaoEncerramentoConta(String motivo) async {
    await _request.post('/SolicitacaoCancelamentoConta', {"motivo_cancelamento": motivo});
  }

  // Future<String> buscarAvatarUsuario() async {
  //   final response = await _request.get<String>('/UsuarioAvatar');
  //   return jsonDecode(response.data);
  // }

  Future<void> cadastrarAvatarUsuario(File avatar) async {
    final multipartFile = MultipartFile.fromBytes(
      avatar.readAsBytesSync(),
      filename: avatar.path.split('/').last,
    );

    final formData = FormData.fromMap({'arquivo': multipartFile});

    await _request.put(
      '/UsuarioAvatar',
      formData,
      encryptionOptions: const EncryptionOptions(shouldEncryptPayload: false),
    );
  }

  Future<void> deletarAvatarUsuario() async {
    await _request.delete('/UsuarioAvatar', null);
  }

  Future<void> savePendingInformation({String? spouseCpf, String? companyCnpj}) async {
    Map<String, dynamic> data = SinacorRequest(convertToString: true).toJson();
    if (spouseCpf != null) data['cpf_conjuge'] = spouseCpf.numericOnly();
    if (companyCnpj != null) data['cnpj_empresa'] = companyCnpj.numericOnly();
    await _request.post('/PessoaAtualizarPendencia', data);
  }

  Future<UserContactData> getUserContactData() async {
    final response = await _request.get('/UsuarioContato');
    return UserContactData.fromJson(response.data);
  }
}
