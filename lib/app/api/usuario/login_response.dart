import 'package:json_annotation/json_annotation.dart';

import 'usuario.dart';

part 'login_response.g.dart';

@JsonSerializable(createToJson: false)
class LoginResponse {
  final String token;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expiredate')
  final DateTime tokenExpirationDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'usuario')
  final Usuario user;

  @Json<PERSON>ey(name: 'termo')
  final bool hasAgreedToCustodyTerms;

  @<PERSON>son<PERSON>ey(name: 'termorpa')
  final bool hasAgreedToRulesAndParameters;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'termorpalink')
  final String rulesAndParametersUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'pendencias')
  final UserPendingInformation? userPendingInformation;

  LoginResponse({
    required this.token,
    required this.user,
    required this.tokenExpirationDate,
    required this.hasAgreedToCustodyTerms,
    required this.hasAgreedToRulesAndParameters,
    required this.rulesAndParametersUrl,
    this.userPendingInformation,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
}

@JsonSerializable(createToJson: false)
class UserPendingInformation {
  @JsonKey(name: 'cpfconjuge')
  bool spouseCpf;
  @JsonKey(name: 'cnpjempresa')
  bool companyCnpj;

  UserPendingInformation({this.spouseCpf = false, this.companyCnpj = false});

  factory UserPendingInformation.fromJson(Map<String, dynamic> json) => _$UserPendingInformationFromJson(json);
}
