import 'package:json_annotation/json_annotation.dart';

part 'usuario_alterar_senha_request.g.dart';

@JsonSerializable()
class UsuarioAlterarSenhaRequest {
  @Json<PERSON>ey(name: 'senha_nova')
  final String newPassword;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'senha_atual')
  final List<List<int>> currentPassword;

  final String token;

  UsuarioAlterarSenhaRequest({
    required this.newPassword,
    required this.currentPassword,
    required this.token,
  });

  factory UsuarioAlterarSenhaRequest.fromJson(Map<String, dynamic> json) => _$UsuarioAlterarSenhaRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioAlterarSenhaRequestToJson(this);
}
