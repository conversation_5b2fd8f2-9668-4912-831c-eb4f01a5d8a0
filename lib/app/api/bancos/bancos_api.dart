import 'package:get/get.dart';

import '../../api/cm_request.dart';

import 'banco.dart';
export 'banco.dart';

import 'pessoa_cadastrar_dados_bancarios_request.dart';
export 'pessoa_cadastrar_dados_bancarios_request.dart';

import '../shared/sinacor_request.dart';

import 'pessoa_remover_dados_bancarios_request.dart';
export 'pessoa_remover_dados_bancarios_request.dart';

import 'referencia_bancaria.dart';
export 'referencia_bancaria.dart';

class BancosApi {
  final _request = Get.find<CMRequest>();

  Future<List<Banco>> bancosListar() async {
    final response = await _request.get('/BancosListar');

    return (response.data as List).map((banco) => Banco.fromJson(banco)).toList();
  }

  Future<List<ReferenciaBancaria>> pessoaReferenciaBancaria() async {
    final response = await _request.post('/PessoaReferenciaBancaria', SinacorRequest(convertToString: true).toJson());

    return (response.data as List).map((referenciaBancaria) => ReferenciaBancaria.fromJson(referenciaBancaria)).toList();
  }

  Future<void> pessoaCadastrarDadosBancarios(PessoaCadastrarDadosBancariosRequest payload) async {
    await _request.post('/PessoaCadastrarDadosBancarios', payload.toJson());
  }

  Future<void> pessoaRemoverDadosBancarios(PessoaRemoverDadosBancariosRequest request) async {
    await _request.post('/PessoaRemoverDadosBancarios', request.toJson());
  }
}
