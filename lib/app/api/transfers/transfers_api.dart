import 'package:get/get.dart';

import '../../models/custody_transfer_request.dart';
import '../../models/period_request.dart';
import '../../models/resource_transfer_request.dart';
import '../../models/transfer/exchange.dart';
import '../../models/transfer/portability_asset.dart';
import '../cm_request.dart';
import '../shared/sinacor_request.dart';
import 'solicitacao_data_hora.dart';

export '../../models/period_request.dart';
export 'solicitacao_data_hora.dart';

class TransfersApi {
  final _request = Get.find<CMRequest>();

  /// Retorna data e horário limite para realização de uma TED na CM.
  Future<SolicitacaoDataHora> horarioLimite() async {
    final response = await _request.get('/SolicitacaoTedHorario');

    return SolicitacaoDataHora.fromJson(response.data);
  }

  Future<ResourceTransferStatus> requestResourceTransfer({
    required int bankId,
    required double amount,
    required DateTime date,
    required List<List<int>> signature,
  }) async {
    final data = SinacorRequest(convertToString: false).toJson()
      ..addAll({
        'pes_ref_banc_id': bankId,
        'ted_solic_valor': amount,
        'ted_solic_data_agend': date.toIso8601String(),
        'assinatura': signature,
      });
    final response = await _request.post('/SolicitacaoTedSalvar', data);
    if (response.data is Map<String, dynamic> && response.data['statussolitacoesid'] is int) {
      return ResourceTransferStatus.fromId(response.data['statussolitacoesid']);
    } else {
      return ResourceTransferStatus.unknown;
    }
  }

  Future<void> cancelResourceTransferRequest(int transferId) async {
    final data = SinacorRequest(convertToString: false).toJson()..addAll({'ted_solic_id': transferId});
    await _request.post('/SolicitacaoTedCancelar', data);
  }

  Future<List<ResourceTransferRequest>> listResourceTransferHistory(PeriodRequest payload) async {
    final response = await _request.post('/SolicitacaoTedListar', payload.toJson());
    return (response.data as List).map((request) => ResourceTransferRequest.fromJson(request)).toList();
  }

  Future<List<CustodyTransferRequest>> listCustodyTransferHistory() async {
    final response = await _request.post('/TransferenciaDeCustodia/Cliente/ListarPapeisSolicitacao', {});
    final responseList = response.data as List<dynamic>;
    if (responseList.isEmpty) return [];

    // Agrupa itens por ID, pois é retornado um item para cada ativo da mesma solicitação
    final transferList = <CustodyTransferRequest>[];
    for (var transferJson in responseList) {
      final transfer = CustodyTransferRequest.fromJson(transferJson);
      if (transferList.contains(transfer)) {
        final index = transferList.indexOf(transfer);
        transferList[index].assets.addAll(transfer.assets);
      } else {
        transferList.add(transfer);
      }
    }
    return transferList;
  }

  Future<CustodyTransferRequest?> getCustodyTransferRequestData(int requestId) {
    return _fetchCustodyTransferRequest(requestId);
  }

  Future<CustodyTransferRequest?> checkForPendingCustodyTransferRequest() {
    return _fetchCustodyTransferRequest();
  }

  Future<CustodyTransferRequest?> _fetchCustodyTransferRequest([int? id]) async {
    final response = await _request.post(
      '/TransferenciaDeCustodia/TransferenciaAtivaNotificacaoApp',
      {'solicitacaoId': id},
    );
    if (response.data == null) return null;
    return CustodyTransferRequest.fromJson(response.data);
  }

  Future<void> validateSignature({required List<List<int>> signature}) {
    final data = {'assinatura': signature};
    return _request.post('/TransferenciaDeCustodia/ValidacaoAssinatura', data);
  }

  Future<void> answerCustodyTransferRequest({required int id, required bool approve, required String token}) {
    final data = {
      'solicitacaoId': id,
      'aprovacao': approve,
      'token': token,
    };
    return _request.post('/TransferenciaDeCustodia/EnviarAutorizacao', data);
  }

  Future<void> postCustodyTransferFeedback({
    required String protocol,
    required String feedback,
  }) {
    final payload = {
      'protocolo': protocol,
      'feedback': feedback,
    };

    return _request.post('/TransferenciaDeCustodia/ReceberFeedbackCliente', payload);
  }

  /// Retorna a lista de solicitações de portabilidade.
  // TODO: Quando a API estiver pronta, este método deve ser atualizado para fazer a chamada real
  Future<List<CustodyTransferRequest>> getPortabilityRequests() async {
    // Simula um atraso de rede
    await Future.delayed(const Duration(seconds: 1));

    // Retorna dados simulados para simular a resposta da API
    return [
      CustodyTransferRequest(
        id: 1,
        protocol: 'PORT-001',
        targetInstitution: 'Banco XYZ',
        requestDate: DateTime.now().subtract(const Duration(days: 2)),
        status: CustodyTransferRequestStatus.pending,
        assets: [
          CustodyTransferRequestAsset(asset: 'PETR4', quantity: 100),
          CustodyTransferRequestAsset(asset: 'VALE3', quantity: 50),
        ],
      ),
    ];
  }

  /// Retorna a lista de corretoras disponíveis para portabilidade.
  // TODO: Quando a API estiver pronta, este método deve ser atualizado para fazer a chamada real
  Future<List<Exchange>> getExchangeList() async {
    // Simula um atraso de rede
    await Future.delayed(const Duration(seconds: 1));

    // Retorna dados simulados para simular a resposta da API
    return [
      Exchange(id: 1, name: 'Ágora Investimentos'),
      Exchange(id: 2, name: 'Ativa Investimentos'),
      Exchange(id: 3, name: 'BTG Pactual'),
      Exchange(id: 4, name: 'C6 Bank'),
      Exchange(id: 5, name: 'Clear Corretora'),
      Exchange(id: 6, name: 'CM Capital'),
      Exchange(id: 7, name: 'Genial Investimentos'),
      Exchange(id: 8, name: 'Itaú Corretora'),
      Exchange(id: 9, name: 'Mirae Investimentos'),
      Exchange(id: 10, name: 'Rico Investimentos'),
      Exchange(id: 11, name: 'Safra Corretora'),
      Exchange(id: 12, name: 'Santander Corretora'),
      Exchange(id: 13, name: 'Toro Investimentos'),
      Exchange(id: 14, name: 'Warren Investimentos'),
      Exchange(id: 15, name: 'XP Investimentos'),
    ];
  }

  /// Retorna a lista de ativos disponíveis para portabilidade.
  Future<List<PortabilityAsset>> getAssetList() async {
    // Simula um atraso de rede
    await Future.delayed(const Duration(seconds: 1));

    // Retorna dados simulados para simular a resposta da API
    return [
      PortabilityAsset(id: 31, type: 'Renda Fixa', name: 'CDB Banco Master S/A', availableQuantity: 15000),
      PortabilityAsset(id: 52, type: 'Renda Fixa', name: 'CDB Will Financeira S/A Crédito Privado', availableQuantity: 5000),
      PortabilityAsset(id: 73, type: 'Renda Fixa', name: 'CDB Banco do Brasil', availableQuantity: 1400793),
      PortabilityAsset(id: 41, type: 'Renda Fixa', name: 'CDB Banco do Nordeste', availableQuantity: 5000),
      PortabilityAsset(id: 44, type: 'Renda Variável', name: 'PETR4', availableQuantity: 100),
      PortabilityAsset(id: 86, type: 'Renda Variável', name: 'VALE3', availableQuantity: 50),
      PortabilityAsset(id: 57, type: 'Renda Variável', name: 'ITUB4', availableQuantity: 25),
      PortabilityAsset(id: 83, type: 'Renda Variável', name: 'BBAS3', availableQuantity: 75),
      PortabilityAsset(id: 29, type: 'Renda Variável', name: 'B3SA3', availableQuantity: 125),
      PortabilityAsset(id: 10, type: 'Proventos', name: 'ABEV3', availableQuantity: 50),
      PortabilityAsset(id: 98, type: 'Fundos', name: 'ORAMA INFLAÇÃO FUNDO DE INVESTIMENTO RENDA FIXA IPCA LONGO PRAZO', availableQuantity: 1000000),
      PortabilityAsset(id: 11, type: 'Fundos', name: 'STOXOS FI EM ACOES', availableQuantity: 35487),
      PortabilityAsset(id: 12, type: 'Fundos', name: 'CLASSE ÚNICA DE COTAS DO HARPIA VALUE INVESTING FI EM AÇÕES - RESPONSABILIDADE LI', availableQuantity: 887455),
      PortabilityAsset(id: 13, type: 'Fundos', name: 'SPX FALCON PVT FIC FI AÇÕES', availableQuantity: 2),
      PortabilityAsset(id: 14, type: 'Fundos', name: 'AXA WF FRAMLINGTON D EC D ADV FIC FIA IE', availableQuantity: 456),
      PortabilityAsset(id: 15, type: 'Fundos', name: 'MANAGER SPX FALCON II FIC FIA', availableQuantity: 1786846),
      PortabilityAsset(id: 16, type: 'Fundos', name: 'AZ QUEST AZI EQUI CHI DOL FIC FIA IE', availableQuantity: 887455),
      PortabilityAsset(id: 17, type: 'Fundos', name: 'Fundo Multimercado', availableQuantity: 200),
      PortabilityAsset(id: 18, type: 'Fundos', name: 'Fundo de Ações', availableQuantity: 1000000),
      PortabilityAsset(id: 19, type: 'Fundos', name: 'Fundo de Renda Fixa', availableQuantity: 35487),
      PortabilityAsset(id: 20, type: 'Fundos', name: 'Fundo', availableQuantity: 887455),
      PortabilityAsset(id: 21, type: 'Fundos', name: 'Fundo', availableQuantity: 2),
      PortabilityAsset(id: 22, type: 'Fundos', name: 'Fundo', availableQuantity: 456),
      PortabilityAsset(id: 23, type: 'Fundos', name: 'Fundo', availableQuantity: 1786846),
      PortabilityAsset(id: 24, type: 'Fundos', name: 'Fundo', availableQuantity: 887455),
      PortabilityAsset(id: 25, type: 'Fundos', name: 'Fundo', availableQuantity: 200),
      PortabilityAsset(id: 26, type: 'Fundos', name: 'Fundo', availableQuantity: 1000000),
      PortabilityAsset(id: 27, type: 'Fundos', name: 'Fundo', availableQuantity: 35487),
    ];
  }
}
