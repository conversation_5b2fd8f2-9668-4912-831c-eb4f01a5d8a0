import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/transfers/transfers_api.dart';
import '../../config/app_theme.dart';
import '../../models/transfer/portability_asset.dart';
import '../../pages/tabs/transfers/portability/portability_request_asset_selection_page.dart';
import '../../utils/extensions.dart';
import '../../models/transfer/exchange.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/input/input.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../user_controller.dart';

class PortabilityToRequestController extends GetxController {
  final _transfersApi = Get.find<TransfersApi>();

  // Seleção de corretora
  final exchanges = <Exchange>[].obs;
  final exchangeFormKey = GlobalKey<FormState>();
  final exchangeFormKeyAutovalidateMode = AutovalidateMode.disabled.obs;
  final selectedExchange = Rx<Exchange?>(null);
  final sinacorCode = ''.obs;
  bool get canSubmitExchangeSelection => selectedExchange.value != null && sinacorCode.value.isNotEmpty;

  Future<void> fetchExchanges() async {
    final exchangeList = await _transfersApi.getExchangeList();
    exchanges.assignAll(exchangeList);
  }

  Future<List<Exchange>> filterExchanges(String text) async {
    if (text.isEmpty) return exchanges;
    return exchanges.where((exchange) => (exchange.name ?? '').matchToSearch(text)).toList();
  }

  Future<void> onExchangeSubmit() async {
    exchangeFormKeyAutovalidateMode.value = AutovalidateMode.always;
    if (exchangeFormKey.currentState?.validate() != true) return;
    Get.toNamed(PortabilityRequestAssetSelectionPage.routeName);
  }

  // Seleção de ativos
  final assets = <PortabilityAsset>[].obs;
  final _isPartialPortabilityAccepted = false.obs;
  bool get isPartialPortabilityAccepted => _isPartialPortabilityAccepted.value;
  String? _motive;
  bool get canSubmitAssetSelection {
    // Se não aceitou a portabilidade parcial, impede a submissão
    if (!isPartialPortabilityAccepted) return false;

    // Verifica se há pelo menos um item selecionado com quantidade válida
    var hasAtLeastOneAssetSelected = false;
    for (var asset in assets) {
      // Caso haja um item com quantidade inválida, impede a submissão
      if (asset.isSelected) {
        hasAtLeastOneAssetSelected = true;
        if ((asset.selectedQuantity ?? 0) <= 0) return false;
      }
    }
    // Se não houver nenhum item selecionado, impede a submissão
    return hasAtLeastOneAssetSelected;
  }

  Future<void> fetchAsstes() async {
    final assetList = await _transfersApi.getAssetList();
    assets.assignAll(assetList);
  }

  void onPartialPortabilityAccepted(bool isChecked) {
    _isPartialPortabilityAccepted.value = isChecked;
  }

  // TODO: definir como deve ser a validação do motivo
  bool _isMotiveInvalid(String? text) => (text ?? '').length < 5;

  Future<void> onAssetSelectionSubmit() async {
    // Solicita motivo, caso ainda não tenha sido informado
    if (_isMotiveInvalid(_motive)) {
      final name = Get.find<UserController>().firstName ?? '';
      final motive = ''.obs;
      final shouldContinue = await CMBottomSheet(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${name.isEmpty ? 'A' : '$name, a'}ntes de seguir..', style: AppTheme.bold18Black),
            const SizedBox(height: 16),
            Text(
                'Queremos sempre oferecer a melhor experiência possível. Se puder nos contar o motivo da sua transferência, isso nos ajuda a entender melhor suas necessidades e melhorar ainda mais nossos serviços',
                style: CMBottomSheet.dialogDescriptionTextStyle),
            const SizedBox(height: 8),
            Input.filled(
              hintText: 'Digite aqui o motivo...',
              textCapitalization: TextCapitalization.sentences,
              onChanged: (text) => motive.value = text,
              maxLines: 5,
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              fillColor: const Color(0xFFF6F5FB),
            ),
            Obx(
              () => Button.elevated(
                margin: const EdgeInsets.only(bottom: 10),
                text: 'Enviar',
                disabled: _isMotiveInvalid(motive.value),
                onPressed: () => Get.back(result: true),
              ),
            ),
          ],
        ),
      ).show();
      if (shouldContinue != true) return;
      _motive = motive.value;

      // Mostra modal de confirmação
      await CMBottomSheet.simple(
        title: 'Obrigado pelo feedback!',
        description: 'Prossiga com a portabilidade dos seus investimentos clicando no botão abaixo.',
        buttonText: 'Prosseguir',
      ).show();
    }

    // TODO: Vai para a tela de resumo
  }
}
