import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api/shared/shared_api.dart';
import '../../api/suitability/suitability_api.dart';
import '../../config/constants.dart';
import '../../errors/error_handlers.dart';
import '../../mixins/asset_purchase_mixin.dart';
import '../../models/trade/trade.dart';
import '../../models/trade/trade_history.dart';
import '../../models/trade/trade_required_balance_calculation.dart';
import '../../pages/tabs/tab_page.dart';
import '../../pages/terms/terms_page.dart';
import '../../utils/pending_information.dart';
import '../../utils/route_history_observer.dart';
import '../../utils/suitability_check.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import 'trade_list_page_controller.dart';

abstract class TradeHiringPageController extends GetxController with AssetMixin, AssetPurchaseMixin {
  final _sharedApi = Get.find<SharedApi>();

  Trade? trade;
  TradeHistory? tradeHistory;

  final investedAmount = 0.0.obs;
  final hasAgreedToTerms = false.obs;
  final minimumBalanceToInvest = 0.0.obs;
  final equationMultiplier = 0.obs;
  final isFetchingMinimumBalance = false.obs;
  final didThrowMinimumBalanceError = false.obs;
  bool get hasMinimumBalanceToInvest => (balance?.availableBalance ?? 0) >= minimumBalanceToInvest.value;
  bool get canInvest => trade != null && hasAgreedToTerms.isTrue && hasMinimumBalanceToInvest && didThrowMinimumBalanceError.isFalse;
  final distributorFeeText = RxnString();

  String get equationText {
    var text = 'O saldo necessário é equivalente a ${equationMultiplier.value}';
    if (trade?.isActive == true) {
      if (minimumBalanceToInvest.value <= 0) {
        text = '*Se o novo valor é inferior ao valor atual, o saldo necessário será R\$ 0,00.';
      } else {
        text += ' vezes a diferença entre os valores.\n\nO saldo não será bloqueado.';
      }
    } else {
      text += ' recomendações. O saldo não será bloqueado.';
    }
    return text;
  }

  @override
  void onInit() {
    super.onInit();
    currencyController.addListener(_updateInvestedAmount);
    debounce<double>(investedAmount, _updateRequiredBalance);
  }

  @override
  void onReady() {
    super.onReady();
    _updateRequiredBalance(investedAmount.value);
  }

  Future<TradeRequiredBalanceCalculation> getRequiredBalance({required int id, required double amount, required bool isActive});
  String get serviceTitle;
  Future<void> editTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  });
  Future<void> hireTrade({
    required int id,
    required double amount,
    required List<List<int>> signature,
    required bool didAcceptTerms,
    required bool isProfileSuitabilityUnfit,
    int? contractCount,
  });
  String get investSuccessMessage;
  String get deactivationConfirmationMessage;
  Future<void> deactivateTrade({required int id, required List<List<int>> signature});
  String get purchaseListPageRouteName;
  String get myInvestmentsListPageRouteName;
  bool get isListPageControllerRegistered;
  TradeListPageController get listPageController;
  Future<Trade> getTradeById(String id);
  String get tradeTile;
  String tradeName(Trade? trade);
  int get tradeTermsId;
  int get distributorFeeTextId;

  Future<void> loadTradeAndBalance() async {
    final hasPendingInformation = await checkPendingInformationForInvesting();
    if (hasPendingInformation) {
      Get.back();
      return;
    }
    await getProduct();
    await _getDistributorFeeText();
    getBalance();
  }

  void _updateInvestedAmount() {
    isFetchingMinimumBalance.value = true;
    didThrowMinimumBalanceError.value = false;
    investedAmount.value = currencyController.numberValue;
    update();
  }

  Future<void> _updateRequiredBalance(double amount) async {
    try {
      final response = await getRequiredBalance(
        id: trade?.id ?? 0,
        amount: max(amount, trade?.minValuePerOperation ?? 0),
        isActive: trade?.isActive == true,
      );
      minimumBalanceToInvest.value = response.requiredBalance ?? 0;
      equationMultiplier.value = response.equationMultiplier ?? 0;
    } catch (error) {
      didThrowMinimumBalanceError.value = true;
      minimumBalanceToInvest.value = 0;
    } finally {
      isFetchingMinimumBalance.value = false;
      update();
    }
  }

  @override
  Future<void> invest() async {
    // Valida formulário
    autovalidateMode.value = AutovalidateMode.always;
    update();
    if (formKey.currentState?.validate() != true) return;

    try {
      // Mostra modal para aceitar desenquadramento de perfil caso seja contratação
      final suitability = await checkInvestmentSuitability(
        productSuitabilityIdOrName: UserSuitabilityProfile.aggressive.text,
        showModal: !trade!.isHired,
        description:
            'O serviço $serviceTitle está em desacordo com seu perfil de investimento. O perfil recomendado para este serviço é Agressivo devido aos riscos e objetivos relacionados. Ao aceitar, declara estar ciente do não enquadramento do seu perfil e assume os riscos inerentes às operações de uma possível contratação.',
        topButtonText: 'Aceitar',
        bottomButtonText: 'Voltar',
      );
      if (!suitability.willContinue) return;

      // Obtém assinatura
      final signature = await getSignature(title: 'Confirmar ${trade!.isActive ? 'alteração' : 'ativação'}');
      if (signature == null) return;

      // Define ação e mensagem para contratação ou edição
      final (apiCall, bottomSheetTitle, bottomSheetDescription) =
          trade!.isActive ? (editTrade, '$serviceTitle atualizado', investSuccessMessage) : (hireTrade, '$serviceTitle ativado', investSuccessMessage);

      // Realiza ação de contratação ou edição
      await apiCall(
        id: trade!.id ?? 0,
        amount: investedAmount.value,
        signature: signature,
        didAcceptTerms: hasAgreedToTerms.value,
        isProfileSuitabilityUnfit: suitability.isProfileSuitabilityUnfit,
        contractCount: int.tryParse(quantityController.text.numericOnly()),
      );

      // Mostra mensagem de confirmação
      await CMBottomSheet.simple(
        topWidget: Image.asset('assets/icons/dialog/treasury_market_suspended.png'),
        title: bottomSheetTitle,
        description: bottomSheetDescription,
      ).show();

      // Retorna à listagem
      _returnToTradeList(didHire: !trade!.isHired);
    } catch (error) {
      onError(error);
    }
  }

  Future<void> confirmTradeDeactivation() async {
    try {
      // Mostra modal para confirmação de desativação
      final hasCanceled = await CMBottomSheet.choice(
        topWidget: Image.asset('assets/icons/dialog/treasury_market_suspended.png'),
        title: 'Desativar $serviceTitle',
        description: deactivationConfirmationMessage,
        topButtonText: 'Desativar',
        topButtonOnPressed: () => Get.back(result: false),
        bottomButtonText: 'Voltar',
        bottomButtonOnPressed: () => Get.back(result: true),
        closeOnConfirm: false,
      ).show<bool>();
      if (hasCanceled ?? true) return;

      // Obtém assinatura
      final signature = await getSignature(
        title: 'Desativar $serviceTitle',
        inputLabel: 'Insira sua assinatura eletrônica para desativar.',
        topWidget: const SizedBox(height: 20),
      );
      if (signature == null) return;

      // Desativa trade
      await deactivateTrade(id: trade!.id ?? 0, signature: signature);

      // Mostra mensagem de confirmação
      await CMBottomSheet.simple(
        topWidget: Image.asset('assets/icons/dialog/treasury_market_suspended.png'),
        title: '$serviceTitle desativado',
        description: 'Você pode ativar este serviço novamente a qualquer momento.',
      ).show();

      // Retorna à listagem
      _returnToTradeList();
    } catch (error) {
      onError(error);
    }
  }

  void _returnToTradeList({bool didHire = false}) {
    final listingPages = [purchaseListPageRouteName, myInvestmentsListPageRouteName];
    final listingPage = routeHistoryObserver.routeNames.lastWhereOrNull((route) => listingPages.contains((route))) ?? purchaseListPageRouteName;

    if (isListPageControllerRegistered) {
      final listController = listPageController;
      if (listingPage == purchaseListPageRouteName && didHire) {
        listController.tabController.animateTo(1);
      }
      listController.loadTrades();
      Get.until(ModalRoute.withName(listingPage));
    } else {
      Get.offNamedUntil(
        listingPage,
        ModalRoute.withName(TabPage.routeName),
        parameters: didHire ? {'tab': '1'} : null,
      );
    }
  }

  @override
  Future<void> getProduct() async {
    if (Get.arguments is Trade) {
      trade = Get.arguments as Trade;
    } else {
      trade = await getTradeById(Get.parameters['id'] ?? '');
    }
  }

  @override
  Map<String, String> get productDetailFields => {};

  @override
  Map<String, String> get signatureDetailFields => {
        tradeTile: tradeName(trade),
        'Valor que será investido em cada recomendação': currencyController.text,
      };

  Future<void> showTerms() async {
    try {
      var termos = await _sharedApi.termosVisualizar(tradeTermsId);
      return Get.toNamed(TermsPage.routeName, arguments: TermsArgs(title: 'Termo de uso do serviço', htmlContent: termos));
    } catch (error) {
      onError(error);
    }
  }

  Future<void> _getDistributorFeeText() async {
    try {
      distributorFeeText.value = await _sharedApi.getDistributorFeeText(id: distributorFeeTextId);
    } catch (error) {
      distributorFeeText.value = null;
    } finally {
      update();
    }
  }

  void updateContractQuantity() {
    if (trade?.minValuePerOperation == null) return;
    final quantity = currencyController.numberValue ~/ Constants.tradeBmfContractValue;
    currencyController.updateValue(currencyController.numberValue);
    addStringContractInInput(quantity.toString());
  }

  void addStringContractInInput(String quantity) {
    quantityController.text = '$quantity contratos';
    quantityController.selection = TextSelection.fromPosition(
      TextPosition(offset: quantity.length),
    );
    update();
  }

  void onContractInputFucusChanged([bool isFocused = false]) {
    if (!isFocused && trade?.minValuePerOperation != null) {
      int minimumNumberContracts = (trade!.minValuePerOperation ?? 0) ~/ Constants.tradeBmfContractValue;
      quantity = int.tryParse(quantityController.text) ?? 0;

      if (quantity < minimumNumberContracts) {
        currencyController.updateValue(0);
        addStringContractInInput('0');
        return;
      }

      currencyController.updateValue(quantity * Constants.tradeBmfContractValue.toDouble());
      addStringContractInInput(quantity.toString());
    }
  }

  void onAmountContractInputFocusChanged([bool isFocused = false]) {
    if (!isFocused && trade?.minValuePerOperation != null) {
      if (currencyController.numberValue < (trade!.minValuePerOperation ?? 0)) {
        currencyController.updateValue(0);
        addStringContractInInput('0');
        return;
      }

      final quantityWithDecimals = currencyController.numberValue / Constants.tradeBmfContractValue;
      final quantityInteger = quantityWithDecimals.truncate();
      if (quantityWithDecimals % 1 != 0) {
        currencyController.updateValue(Constants.tradeBmfContractValue * quantityInteger.toDouble());
      }
      addStringContractInInput(quantityInteger.toString());
    }
  }
}
