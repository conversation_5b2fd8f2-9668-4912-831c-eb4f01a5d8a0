import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../api/cm_request.dart';
import '../api/notifications/notification_api.dart';
import '../api/usuario/usuario.dart';
import '../api/usuario/usuario_api.dart';
import '../api/usuario/usuario_device_acessar_request.dart';
import '../api/usuario/usuario_device_verificar_request.dart';
import '../api/usuario/usuario_login_v2_request.dart';
import '../config/app_theme.dart';
import '../config/constants.dart';
import '../errors/error_handlers.dart';
import '../models/notification/notification_new_functionality.dart';
import '../models/user_contact_data.dart';
import '../pages/auth/first_access/first_access_initial_page.dart';
import '../pages/auth/login/confirmation_login_page.dart';
import '../pages/auth/login/new_login_password_page.dart';
import '../pages/auth/onboarding_page.dart';
import '../pages/profile/account_security_page.dart';
import '../pages/tabs/tab_page.dart';
import '../plugins/onesignal.dart';
import '../utils/extensions.dart';
import '../utils/get_file.dart';
import '../utils/local_storage.dart';
import '../utils/ui_utils.dart';
import '../utils/url_handlers.dart';
import '../utils/validators.dart';
import '../widgets/info/decorated_text.dart';
import '../widgets/modal/cm_dialog.dart';
import '../widgets/input/input.dart';
import '../widgets/modal/cm_bottom_sheet.dart';
import '../widgets/modal/pix_modals.dart.dart';
import 'biometrics_controller.dart';
import 'rules_and_parameters_controller.dart';
import 'user_controller.dart';

class LoginController extends GetxController {
  final _userController = Get.find<UserController>();
  final _usuarioApi = Get.find<UsuarioApi>();
  final _notificationApi = Get.find<NotificationApi>();
  final _rulesAndParametersController = Get.find<RulesAndParametersController>();
  final _biometricsController = BiometricsController();
  final _storage = LocalStorage();
  final _localAuthentication = LocalAuthentication();

  final inputLabelFontStyle = Input.inputLabelStyle.copyWith(fontSize: 14, fontWeight: AppFontWeight.regular);
  final inputTextFontStyle = AppTheme.defaultText.copyWith(fontSize: 32);
  final inputHintFontStyle = Input.inputHintStyle.copyWith(fontSize: 32, color: AppTheme.whiteColor.withOpacity(0.2));

  final cpfFormKey = GlobalKey<FormState>();
  final passwordFormKey = GlobalKey<FormState>();
  final confirmationFormKey = GlobalKey<FormState>();

  final credentialsFormKey = GlobalKey<FormState>();
  final loginController = TextEditingController();
  final passwordController = TextEditingController();
  final loginFormatter = MaskTextInputFormatter(mask: '###.###.###-##', filter: {"#": RegExp(r'[0-9]')});
  final password = <List<int>>[];
  bool remindAccount = true;
  bool isLoading = false;
  bool showBiometricLogin = false;
  bool showModalFirstAccess = false;

  // Dados de login
  String? uuid;
  String? loginCode;
  String? userLogin;
  String? userName;

  bool get hasBiometricCredentials => uuid != null && loginCode != null;
  bool get isFirstAccess => [uuid, loginCode, userLogin, userName].every((element) => element == null);

  // Validação do login
  final validationFormKey = GlobalKey<FormState>();
  var validator = LoginValidator.getRandom();
  String validationValue = '';

  @override
  void onInit() {
    super.onInit();
    loginController.addListener(update);
    getSavedBiometricsData();
    getSavedCredentialsData();
  }

  Future<void> getSavedBiometricsData() async {
    uuid = await _storage.read<String>(LocalStorageKeys.uuid);
    loginCode = await _storage.read<String>(LocalStorageKeys.loginCode);

    if (uuid != null && loginCode != null) {
      showBiometricLogin = true;
      update();
    }

    final avatarPath = await _storage.read<String>(LocalStorageKeys.avatar);
    if (avatarPath == null) return;

    final file = File(avatarPath);
    if (file.existsSync()) {
      _userController.avatar.value = file;
    } else {
      await _storage.delete(LocalStorageKeys.avatar);
    }
  }

  void getSavedCredentialsData() async {
    userLogin = await _storage.read<String>(LocalStorageKeys.userLogin);
    userName = await _storage.read<String>(LocalStorageKeys.userName);

    if (userLogin == null) return;

    if (userLogin!.length > 9) {
      loginController.value = loginFormatter.updateMask(mask: '###.###.###-##');
      loginController.text = loginFormatter.maskText(userLogin!).replaceRange(0, 7, 'XXX.XXX');
    } else {
      loginController.value = loginFormatter.updateMask(mask: '##########');
      loginController.text = userLogin!;
    }
    update();
  }

  /// Obtém e salva alguns dados iniciais do usuário que serão reutilizados no app durante a sessão de uso.
  Future<void> setUserData() async {
    // Salva código do usuário para login via biometria posteriormente
    _storage.write(LocalStorageKeys.loginCode, _userController.usuario?.codigo.toString());

    // Persiste dados no app
    if (remindAccount) {
      _storage.write(LocalStorageKeys.userLogin, userLogin);
      _storage.write(LocalStorageKeys.userName, _userController.usuario?.nome.capitalize);
    }

    if ((_userController.usuario?.avatar ?? '').isNotEmpty) {
      final url = Uri.parse(_userController.usuario!.avatar!);
      final file = await getFileFromUrl(url, url.pathSegments.last);

      _userController.avatar.value = file;

      _storage.write(LocalStorageKeys.avatar, file.path);
    }

    // Contas bancárias
    await _usuarioApi.usuarioConta();

    // Dados sobre usuário
    _userController.usuarioDados = await _usuarioApi.usuarioDados();

    // CPF mascarado
    UsuarioCPFResponse usuarioCPFResponse = await _usuarioApi.usuarioCPF();
    String maskedCPF = MaskTextInputFormatter(mask: '###.###.###-##').maskText(usuarioCPFResponse.cpf);
    _userController.usuarioMaskedCPF = maskedCPF.replaceRange(0, 7, 'XXX.XXX');
  }

  void onLoginChanged(String text) {
    final unmaskedText = loginFormatter.unmaskText(text);
    userLogin = unmaskedText;
    if (unmaskedText.length > 9) {
      loginController.value = loginFormatter.updateMask(mask: '###.###.###-##');
      if (validator == LoginValidator.cpf3digits) validator = LoginValidator.getRandom(includeCpf: false);
    } else {
      loginController.value = loginFormatter.updateMask(mask: '###########');
    }
  }

  String? validateLoginField(String? text) => validateLogin(loginFormatter.unmaskText(loginController.text));

  Future<void> submitNewLoginCpf() async {
    if (cpfFormKey.currentState?.validate() != true) return;
    isLoading = true;
    update();
    try {
      final isFirstAccess = await _usuarioApi.usuarioVerificaPrimeiroAcesso(loginController.text.numericOnly());
      showModalFirstAccess = isFirstAccess;
      if (isFirstAccess) {
        Get.toNamed(FirstAccessInitialPage.routeName);
      } else {
        Get.toNamed(NewLoginPasswordPage.routeName);
      }
    } catch (error) {
      onError(error);
    } finally {
      isLoading = false;
      update();
    }
  }

  void submitPassword() {
    if (passwordFormKey.currentState?.validate() != true) return;
    Get.toNamed(ConfirmationLoginPage.routeName);
  }

  Future<void> submitConfirmation() async {
    if (confirmationFormKey.currentState?.validate() != true) return;

    isLoading = true;
    update();
    try {
      await login(
        loginApiCall: () => _usuarioApi.usuarioLoginV2(
          UsuarioLoginV2Request(
            login: userLogin ?? '',
            senha: password,
            validador: validator.id,
            valor: validationValue,
          ),
        ),
      );
    } finally {
      isLoading = false;
      update();
    }
  }

  void onLoginViaBiometrics() async {
    if (await validateBiometrics() == false) {
      return showBiometricsValidationErrorDialog();
    }

    await login(
      loginApiCall: () => _usuarioApi.usuarioDeviceAcessar(
        UsuarioDeviceAcessarRequest(
          guid: uuid ?? '',
          usuario: loginCode ?? '',
        ),
      ),
    ).catchError((_) {});
  }

  Future<void> showBiometricsValidationErrorDialog() async {
    await CMBottomSheet.simple(
      title: 'Erro ao entrar',
      description: 'Não foi possível realizar o login por biometria. Entre utilizando seu CPF e senha.',
      buttonText: 'FECHAR',
    ).show();
  }

  Future<void> login({required Future<LoginResponse> Function() loginApiCall}) async {
    isLoading = true;
    update();

    try {
      final loginResponse = await loginApiCall();

      final isLegalPerson = await checkIfIsLegalPerson(loginResponse.user);
      if (isLegalPerson) return;

      // Armazena dados temporários da sessão
      Get.find<CMRequest>().token = loginResponse.token;
      _userController.usuario = loginResponse.user;
      _userController.pendingInformation = loginResponse.userPendingInformation;

      // Verifica se é necessário aceitar termos de custódia
      if (loginResponse.hasAgreedToCustodyTerms == false) {
        final didAcceptTerms = await showCustodyTermsDialog();
        if (didAcceptTerms) login(loginApiCall: loginApiCall);
        return;
      }

      // Atualiza URL do documento de regras e parâmetros de atuação
      _rulesAndParametersController.documentUrl.value = loginResponse.rulesAndParametersUrl;

      // Verifica se é necessário aceitar regras e parâmetros de atuação
      if (loginResponse.hasAgreedToRulesAndParameters == false) {
        final didAcceptRules = await _rulesAndParametersController.showUpdateWarning();
        if (didAcceptRules) await login(loginApiCall: loginApiCall);
        return;
      }

      await setUserData();
      setupPlayerId();
      showDialogLogin();
      _deleteController();
      return Get.offAllNamed(TabPage.routeName);
    } catch (error) {
      await onError(error);
      rethrow;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> showDialogLogin() async {
    // modal pix primeiro acesso
    if (showModalFirstAccess) {
      //necessario manter esse delay para que o modal seja apresentado!
      await Future.delayed(const Duration(seconds: 2));
      await PixModals.keyInformationBottomSheet.show();
      return;
    }

    List<NotificationNewFunctionality> listNewFunctionality = [];

    try {
      listNewFunctionality = await _notificationApi.listNewFunctionality();
    } catch (_) {}

    // Procura por uma funcionalidade relacionada ao "pix"
    final pixNotification = listNewFunctionality.firstWhereOrNull((notification) => notification.name != null && notification.name!.toLowerCase() == "pix");

    if (pixNotification?.status == true) {
      //modal pix usuario ja existe, apresenta nova funcionalidade
      await PixModals.newDepositMethodBottomSheet.show();
      // confirmar visualização da nova funcionalidade:
      await _notificationApi.confirmNewFunctionalityView(pixNotification!.id!);
      return;
    }

    // Procura por uma funcionalidade relacionada ao MFA
    final mfaNotification = listNewFunctionality.firstWhereOrNull((notification) => notification.name != null && notification.name!.toLowerCase() == "mfa");

    if (mfaNotification?.status == true) {
      // Mostra modal sobre MFA
      await _showMfaActivationWarning();
      // Confirma visualização de modal
      await _notificationApi.confirmNewFunctionalityView(mfaNotification!.id!);
      return;
    }

    //modal biometria será apresentado proximo login caso o modal de nova funcionalidade seja apresentado
    await showBiometricsDialog();
  }

  Future<void> _showMfaActivationWarning() async {
    UserContactData? userContactData;
    try {
      userContactData = await _usuarioApi.getUserContactData();
    } catch (_) {}
    final defaultTextStyle = CMBottomSheet.dialogDescriptionTextStyle;
    final decoratedTextStyle = CMBottomSheet.boldDialogDescriptionTextStyle;
    const paragraphSpacing = 12.0;
    return CMBottomSheet.choice(
      topWidget: Image.asset('assets/icons/dialog/password_lock_thumbs_up.png', height: 150),
      title: 'Atenção! Mudanças no acesso à sua conta',
      descriptionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DecoratedText('{${_userController.firstName ?? 'Cliente'}}, em breve será necessário informar um código de segurança para acessar sua conta.',
              textStyle: defaultTextStyle, decoratedTextStyle: decoratedTextStyle),
          const SizedBox(height: paragraphSpacing),
          Text('Confira se as informações abaixo estão atualizadas para recebimento dos códigos.', style: CMBottomSheet.boldDialogDescriptionTextStyle),
          const SizedBox(height: paragraphSpacing),
          DecoratedText('{E-mail}: ${userContactData?.email ?? 'Indefinido'}', textStyle: defaultTextStyle, decoratedTextStyle: decoratedTextStyle),
          const SizedBox(height: paragraphSpacing),
          DecoratedText('{Celular}: ${userContactData?.phoneNumber?.brazilianPhoneCodeRemoved.maskedPhoneNumber ?? 'Indefinido'}',
              textStyle: defaultTextStyle, decoratedTextStyle: decoratedTextStyle),
          const SizedBox(height: paragraphSpacing),
          Text('Caso as informações estejam incorretas, confira as instruções para atualização clicando no botão abaixo.',
              style: CMBottomSheet.dialogDescriptionTextStyle.copyWith(fontSize: 12)),
        ],
      ),
      topButtonText: 'Segurança da Conta',
      bottomButtonText: 'Fechar',
      topButtonOnPressed: () => Get.toNamedAndPopExistent(AccountSecurityPage.routeName),
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  /// Remove o controller da memória, caso não seja removido sozinho.
  /// Isso está sendo utilizado pois na tela de login por biometria o controller está com `autoRemove: false`, para evitar que seja removido ao ir para a tela de login por senha.
  void _deleteController() {
    if (Get.isRegistered<LoginController>()) {
      Get.delete<LoginController>();
    }
  }

  Future<void> setupPlayerId() async {
    try {
      final playerId = await getPushNotificationsPlayerId();
      if (playerId != null) {
        await _notificationApi.registerPushNotificationDevice(playerId);
      }
    } catch (_) {}
  }

  void onSwapAccount() {
    CMBottomSheet.choice(
      title: 'Deseja sair?',
      description: 'Confirme se deseja limpar os dados da conta atual e você será direcionado para o login.',
      topButtonText: 'NÃO',
      topButtonOnPressed: Get.back,
      isTopButtonOutlined: true,
      bottomButtonText: 'SIM',
      bottomButtonOnPressed: () async {
        _userController.deleteAvatarLocalCopy();
        await _storage.deleteAll();
        _deleteController();
        return Get.offAllNamed(OnboardingPage.routeName);
      },
      isBottomButtonOutlined: false,
      buttonLayoutDirection: Axis.horizontal,
    ).show();
  }

  Future<void> showBiometricsDialog() async {
    if (await _localAuthentication.canCheckBiometrics && !await _storage.containsKey(LocalStorageKeys.shownBiometricsReminderModal)) {
      // Checa se usuário já tem biometria cadastrada e é biometria atual
      String uuid = await _storage.read<String>(LocalStorageKeys.uuid) ?? generateUuid();

      final biometricsData = await _usuarioApi.usuarioDeviceVerificar(UsuarioDeviceVerificarRequest(guid: uuid));

      // Checa biometria se usuário ainda não tiver visto o modal.
      if (biometricsData.currentGuid == false) {
        await _storage.write(LocalStorageKeys.shownBiometricsReminderModal, true);

        final shouldRegisterBiometrics = await CMBottomSheet.choice(
          title: 'Cadastrar biometria',
          description:
              'Deseja cadastrar sua biometria para este dispositivo nos próximos acessos?\n\nVocê também pode cadastrá-la em outro momento, quando quiser na aba “Segurança”.',
          topButtonText: 'Sim',
          topButtonOnPressed: () async {
            Get.back();
            await validateBiometrics();
          },
          bottomButtonText: 'Não',
          bottomButtonOnPressed: Get.back,
          topWidget: Image.asset('assets/icons/dialog/biometrics_alert.png'),
        ).show();

        if (shouldRegisterBiometrics == true) _biometricsController.registerBiometrics();
      }
    }
  }

  Future<bool> checkIfIsLegalPerson(Usuario user) async {
    if (user.pessoa == "PJ") {
      final websiteUrl = Constants.apiUrl.replaceAll('/api', '/');
      await CMDialog.warning(
        title: "Aviso",
        description: "Sua conta é de pessoa jurídica. Realize o acesso pelo Portal Web.",
        cancelButtonText: "Voltar",
        confirmButtonText: "Abrir Portal",
        confirmButtonOnPressed: () => openUrl(
          websiteUrl,
          useExternalApplicationForIos: true,
        ),
      ).show();
      Get.back();
      return true;
    } else {
      return false;
    }
  }

  Future<bool> showCustodyTermsDialog() async {
    final didAcceptTerms = await CMBottomSheet.choice(
      title: 'Atualização de contrato de intermediação e custódia',
      description: 'Prezado(a) Cliente,\n\n'
          'Os contratos de intermediação foram alterados para prever o disposto na Res. Conjunta nº 6 de 2023 relacionada ao compartilhamento de dados entre instituições financeiras para fins de prevenção à fraude. Dessa forma, foi inserida a Cláusula de Compartilhamento de Informações para prever tal mecanismo antifraude.\n\n'
          'Recomendamos a leitura do documento, disponível para download abaixo.\n\n'
          'Para continuidade das operações na CM Capital, pedimos que aceite o termo de adesão do novo contrato vigente.',
      canPop: false,
      closeOnConfirm: false,
      topButtonText: 'Aceitar contrato',
      topButtonOnPressed: () async {
        try {
          await _usuarioApi.usuarioValidarTermosV2();
          Get.back(result: true);
        } catch (error) {
          Get.back(result: false);
          onError(error);
        }
      },
      bottomButtonText: 'Baixar contrato',
      bottomButtonOnPressed: () async {
        final fileUrl = '${Constants.apiUrl.replaceAll('/api', '/')}/assets/arquivos/2.091.117.pdf';
        final fileName = Uri.parse(fileUrl).pathSegments.last;
        await downloadAndOpenFile(fileUrl, fileName);
      },
    ).show<bool?>();
    return didAcceptTerms ?? false;
  }
}

enum LoginValidator {
  birthDay(
    id: 1,
    textMaxLength: 2,
    hint: 'Digite o dia do nascimento',
  ),
  birthMonth(
    id: 2,
    textMaxLength: 2,
    hint: 'Digite o mês do nascimento',
  ),
  birthYear(
    id: 3,
    textMaxLength: 4,
    hint: 'Digite o ano do nascimento',
  ),
  cpf3digits(
    id: 4,
    textMaxLength: 3,
    hint: 'Digite os 3 primeiros dígitos do CPF',
  );

  final int id;
  final String hint;
  final int textMaxLength;

  static LoginValidator getRandom({bool includeCpf = true}) {
    final max = values.length - (includeCpf ? 0 : 1);
    return values[Random().nextInt(max)];
  }

  const LoginValidator({
    required this.id,
    required this.textMaxLength,
    required this.hint,
  });
}
