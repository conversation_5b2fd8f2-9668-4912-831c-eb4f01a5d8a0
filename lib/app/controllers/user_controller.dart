import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../api/cm_request.dart';
import '../api/shared/shared_api.dart';
import '../api/usuario/user_classification.dart';
import '../api/usuario/usuario.dart';
import '../api/usuario/usuario_api.dart';
import '../api/usuario/usuario_dados.dart';
import '../config/app_theme.dart';
import '../config/constants.dart';
import '../errors/error_handlers.dart';
import '../pages/auth/login/continue_login_biometrics_page.dart';
import '../pages/auth/login/continue_login_password_page.dart';
import '../pages/auth/onboarding_page.dart';
import '../utils/extensions.dart';
import '../utils/local_storage.dart';
import '../utils/ui_utils.dart';
import '../utils/url_handlers.dart';
import '../widgets/info/badge.dart';
import '../widgets/modal/cm_bottom_sheet.dart';
import '../widgets/info/decorated_text.dart';
import '../widgets/input/labeled_checkbox.dart';

class UserController extends GetxController {
  Usuario? usuario;
  UsuarioDados? _usuarioDados;
  UserPendingInformation? pendingInformation;

  var qualificationTerm = "";
  var didFailToLoadQualificationTerm = false;
  final isFetchingQualificationTerm = RxBool(false);
  final isQualifiedInvestor = RxBool(false);

  UsuarioDados? get usuarioDados {
    return _usuarioDados;
  }

  set usuarioDados(UsuarioDados? value) {
    _usuarioDados = value;
    isQualifiedInvestor.value = usuarioDados?.classificationEnum.isQualifiedInvestor ?? false;
  }

  List<Conta> accounts = [];
  Conta? currentAccount;
  int? get sinacor => currentAccount?.account;

  String? get firstName => usuario?.nome.firstWord.capitalize;

  String usuarioMaskedCPF = '';

  final avatar = Rxn<File>();

  bool get isLoggedIn => usuario != null;

  Future<void> ensureUsuarioDadosIsSet() async {
    if ((usuarioDados as dynamic) == null) {
      final usuarioApi = Get.find<UsuarioApi>();
      usuarioDados = await usuarioApi.usuarioDados();
    }
  }

  Future<void> ensureQualificationTermIsLoaded() async {
    final sharedApi = Get.find<SharedApi>();

    try {
      if (didFailToLoadQualificationTerm || qualificationTerm.isEmpty) {
        isFetchingQualificationTerm.value = true;
        final term = await sharedApi.termosVisualizar(29);
        if (term?.isEmpty ?? true) {
          throw Error();
        }

        qualificationTerm = term!;
        didFailToLoadQualificationTerm = false;
      }
    } catch (error) {
      qualificationTerm = "<strong>Ocorreu um erro ao carregar o termo. Tente novamente.</strong>";
      didFailToLoadQualificationTerm = true;
    } finally {
      isFetchingQualificationTerm.value = false;
    }
  }

  Future<void> openQualificationTermBottomSheet({void Function()? onButtonPressed}) async {
    await ensureQualificationTermIsLoaded();

    return CMBottomSheet.choice(
      title: "Termo de Ciência de Risco",
      descriptionWidget: getWidgetFromHtml(
        qualificationTerm,
        color: CMBottomSheet.dialogDescriptionTextStyle.color,
        fontSize: AppTheme.semi16Black.fontSize,
      ),
      topButtonText: "DECLARO TER LIDO O TERMO",
      bottomButtonText: "CANCELAR",
      topButtonOnPressed: () => onButtonPressed != null ? onButtonPressed() : openQualificationConfirmBottomSheet(isDeclaredAsRead: true),
      bottomButtonOnPressed: Get.back,
    ).show();
  }

  Future<void> openQualificationConfirmBottomSheet({bool isDeclaredAsRead = false}) async {
    final usuarioApi = Get.find<UsuarioApi>();
    final hasAcceptedTerms = isDeclaredAsRead.obs;

    late CMBottomSheet lateBottomSheet;
    final bottomSheet = CMBottomSheet.choice(
      title: "Meu Status atual",
      topButtonLeftIcon: Icons.how_to_reg,
      closeOnConfirm: false,
      descriptionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CMBadge(
            text: "NÃO QUALIFICADO",
            textStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            margin: EdgeInsets.zero,
            backgroundColor: AppTheme.redColor,
          ),
          const SizedBox(height: 16),
          Text(
            "Você não é um Investidor Qualificado.",
            style: CMBottomSheet.dialogDescriptionTextStyle.copyWith(
              fontWeight: AppTheme.bold16Black.fontWeight,
            ),
          ),
          const SizedBox(height: 24),
          DecoratedText(
            "O conceito de Investidor Qualificado é, na verdade, uma classificação, considerando o nível de conhecimento e capacidade de avaliação de riscos do investidor. Conforme definido pela CVM (Comissão de Valores Mobiliários) para se qualificar como Investidor Qualificado, o cliente deve possuir mais de R\$ 1 milhão investidos em produtos financeiros e/ou ter sido aprovado em exames de qualificação técnica ou certificações aprovadas pela CVM.\nA principal vantagem em ser um Investidor Qualificado é ter acesso a produtos financeiros restritos, com taxas de retorno mais atrativas, porém com riscos mais altos.\n\nO cliente poderá se auto declarar como Investidor Qualificado após aceitar o termo disponível neste menu. O referido termo deverá ser aceito antes de realizar qualquer investimento. Caso possua alguma das certificações ou exame de qualificação técnica que o habilite como Investidor Qualificado, basta entrar em contato com nossa Central de Atendimento e enviar o comprovante equivalente.\n\nAinda com dúvidas? Assista ao vídeo: \n{${Constants.qualifiedInvestorVideoUrl}}\n\nSe estiver dentro das condições elegíveis, leia e aceite o termo abaixo.",
            textStyle: CMBottomSheet.dialogDescriptionTextStyle,
            actions: [TapGestureRecognizer()..onTap = () => openUrl(Constants.qualifiedInvestorVideoUrl)],
            decoratedTextStyle: CMBottomSheet.dialogDescriptionTextStyle.copyWith(
              fontWeight: AppTheme.bold16Black.fontWeight,
              decoration: TextDecoration.underline,
            ),
          ),
          const SizedBox(height: 24),
          Obx(
            () => LabeledCheckbox(
              value: hasAcceptedTerms.value,
              customLabel: DecoratedText(
                'Declaro ter lido o {Termo Investidor Qualificado}',
                textStyle: CMBottomSheet.dialogDescriptionTextStyle,
                decoratedTextStyle: AppTheme.bold16Orange,
                actions: [
                  TapGestureRecognizer()
                    ..onTap = () {
                      Get.back();
                      openQualificationTermBottomSheet();
                    }
                ],
              ),
              onChanged: (value) {
                hasAcceptedTerms.value = value;
                lateBottomSheet.isTopButtonDisabled.value = !value;
              },
            ),
          ),
        ],
      ),
      topButtonText: "SOU INVESTIDOR QUALIFICADO",
      bottomButtonText: "CANCELAR",
      topButtonOnPressed: () async {
        try {
          await usuarioApi.gravarClassificacao(UserClassification.qualified);
          usuarioDados!.classificacao = UserClassification.qualified.id;
          isQualifiedInvestor.value = true;

          Get.back();
          CMBottomSheet.simple(
                  title: "Investidor Qualificado",
                  description: "Tudo certo, agora seu perfil esta apto para comprar produtos com selo investidor qualificado.",
                  topWidget: Image.asset("assets/icons/dialog/redeem_requested.png"),
                  buttonText: "OK, ENTENDI",
                  isTopButtonOutlined: true)
              .show();
        } catch (error) {
          Get.back();
          onError(error);
        }
      },
      bottomButtonOnPressed: Get.back,
    );
    lateBottomSheet = bottomSheet;
    lateBottomSheet.isTopButtonDisabled.value = !hasAcceptedTerms.value;

    return lateBottomSheet.show();
  }

  Future<void> deleteAvatarLocalCopy() async {
    if (avatar.value?.existsSync() == true) avatar.value?.deleteSync();
    avatar.value = null;

    LocalStorage().delete(LocalStorageKeys.avatar);
  }

  Future<void> returnToLogin() async {
    final storage = LocalStorage();
    final hasSavedLoginCode = await storage.containsKey(LocalStorageKeys.loginCode);
    final hasSavedUuid = await storage.containsKey(LocalStorageKeys.uuid);

    var pageName = OnboardingPage.routeName;
    if (hasSavedUuid && hasSavedLoginCode) {
      pageName = ContinueLoginBiometricsPage.routeName;
    } else if (hasSavedLoginCode) {
      pageName = ContinueLoginPasswordPage.routeName;
    } else {
      storage.delete(LocalStorageKeys.userLogin);
      storage.delete(LocalStorageKeys.userName);
      storage.delete(LocalStorageKeys.avatar);
    }
    Get.offAllNamed(pageName);

    Get.find<CMRequest>().token = null;
    usuario = null;
  }
}
