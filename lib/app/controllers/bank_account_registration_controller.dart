import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../api/bancos/bancos_api.dart';
import '../api/usuario/usuario_api.dart';
import '../errors/error_handlers.dart';
import '../pages/profile/bank_account_signature_page.dart';
// import '../pages/profile/bank_account_token_page.dart';
import '../pages/profile/bank_accounts_page.dart';
import '../pages/tabs/transfers/transfer_resources_page.dart';
import '../utils/validators.dart';
import '../widgets/modal/cm_bottom_sheet.dart';
import 'transfers/transfer_resources_page_controller.dart';

// const _resendTokenTime = 30;

class BankAccountRegistrationController extends GetxController {
  final _usuarioApi = Get.find<UsuarioApi>();
  final _bancoApi = Get.find<BancosApi>();

  // Formulário de conta
  final accountFormKey = GlobalKey<FormState>();
  final accountFormValidateMode = AutovalidateMode.disabled.obs;
  final accountData = PessoaCadastrarDadosBancariosRequest(conjunta: "false").obs;
  final bankList = <Banco>[].obs;
  final isLoadingBanks = false.obs;
  final selectedBank = Rx<Banco?>(null);
  bool get isJointAccount => accountData.value.conjunta?.toLowerCase() == 'true';
  final userCpf = Rx<String?>(null);
  final isSubmittingAccountData = false.obs;

  // Formulário de assinatura
  final signatureFormKey = GlobalKey<FormState>();
  final signature = <List<int>>[];
  String get bankName => selectedBank.value?.name ?? '';

  // Conclusão
  late final String sourceRoute;

  // TODO: utilizar código relacionado ao token quando back estiver atualizado
  // // Formulário de token
  // final tokenFormKey = GlobalKey<FormState>();
  // final tokenFormValidateMode = AutovalidateMode.disabled.obs;
  // final token = ''.obs;
  // final isRequestingToken = false.obs;
  // final _secondsToResendToken = _resendTokenTime.obs;
  // bool get isCountingDownToResend => _secondsToResendToken.value < _resendTokenTime;
  // String get resendText {
  //   var text = 'Enviar código novamente';
  //   if (isCountingDownToResend) text += '   ${_secondsToResendToken.value}';
  //   return text;
  // }

  final isCreatingBankAccount = false.obs;

  @override
  void onInit() {
    super.onInit();
    sourceRoute = Get.previousRoute;
    _getBankList();
  }

  void _getBankList() async {
    isLoadingBanks.value = true;
    try {
      // Obtém bancos
      final List<Banco> bancosResponse = await _bancoApi.bancosListar();

      // Separa bancos entre 'principais' e 'outros'
      final List<List<Banco>> separatedBanks = [[], []];
      for (var banco in bancosResponse) {
        if (banco.type == 'Principais') {
          separatedBanks[0].add(banco);
        } else {
          separatedBanks[1].add(banco);
        }
      }
      final List<Banco> joinedBanks = separatedBanks.expand((bank) => bank).toList();

      // Lista bancos
      bankList.addAll(joinedBanks);

      userCpf.value = (await _usuarioApi.usuarioCPF()).cpf;
    } finally {
      isLoadingBanks.value = false;
    }
  }

  String? validateBank(_) {
    if (accountData.value.banco == null || (accountData.value.banco ?? '').isEmpty) {
      return 'É necessário escolher o banco.';
    } else {
      return null;
    }
  }

  String? validateJointAccountCpf(String? text) {
    final cpfNumbers = (text ?? '').numericOnly();
    String? errorMessage = validateCPF(cpfNumbers);
    if (cpfNumbers == userCpf.value) errorMessage = 'O CPF não pode ser o mesmo do titular.';
    return errorMessage;
  }

  void submitAccountData() {
    accountFormValidateMode.value = AutovalidateMode.always;
    if (accountFormKey.currentState?.validate() != true) return;
    Get.toNamed(BankAccountSignaturePage.routeName);
  }

  // Future<void> requestToken({bool isTokenPage = false}) async {
  //   if (!isTokenPage) {
  //     signatureFormValidateMode.value = AutovalidateMode.always;
  //     if (signatureFormKey.currentState?.validate() != true) return;
  //   }

  //   isRequestingToken.value = true;
  //   try {
  //     await Future.delayed(const Duration(seconds: 1));
  //     if (isTokenPage) {
  //       _startResendButtonCountdown();
  //     } else {
  //       Get.toNamed(BankAccountTokenPage.routeName);
  //     }
  //   } catch (error) {
  //     onError(error);
  //   } finally {
  //     isRequestingToken.value = false;
  //   }
  // }

  // void _startResendButtonCountdown() {
  //   _secondsToResendToken.value--;
  //   Timer.periodic(
  //     const Duration(seconds: 1),
  //     (timer) {
  //       _secondsToResendToken.value--;
  //       if (_secondsToResendToken.value < 1) {
  //         _secondsToResendToken.value = _resendTokenTime;
  //         timer.cancel();
  //       }
  //     },
  //   );
  // }

  Future<void> createBankAccount() async {
    if (signatureFormKey.currentState?.validate() != true) return;

    isCreatingBankAccount.value = true;
    try {
      // Cadastra conta bancária
      accountData.value.banco = selectedBank.value?.id.toString();
      accountData.value.cpfConjunta = accountData.value.cpfConjunta?.numericOnly();
      accountData.value.assinatura = signature;
      await _bancoApi.pessoaCadastrarDadosBancarios(accountData.value);

      // Mostra alerta de conclusão
      _showConclusionDialog();
    } catch (error) {
      onError(error);
    } finally {
      isCreatingBankAccount.value = false;
    }
  }

  void _showConclusionDialog() {
    // Informações do alerta
    final dialogIcon = Image.asset('assets/icons/dialog/bank_account_registration.png');
    const dialogTitle = 'Conta bancária';
    var dialogDescription = 'Tudo certo, sua conta bancária foi cadastrada com sucesso.';

    // Mostra alerta com opção de ir para a tela de transferência
    if (sourceRoute == BankAccountsPage.routeName) {
      dialogDescription += '\n\nDeseja realizar uma transferência de recursos?';
      CMBottomSheet.choice(
        topWidget: dialogIcon,
        title: dialogTitle,
        description: dialogDescription,
        canPop: false,
        topButtonText: 'Sim',
        topButtonOnPressed: () => Get.offNamedUntil(TransferResourcesPage.routeName, (route) => route.isFirst),
        bottomButtonText: 'Não',
        bottomButtonOnPressed: _returnToSourceRoute,
      ).show();

      // Mostra alerta que retorna para a tela que iniciou o cadastro
    } else {
      // Caso seja a tela de transferência, atualiza [hasNewBankAccountBeenCreated] para que conta selecionada seja a nova
      if (Get.isRegistered<TransferResourcesPageController>()) {
        final transferController = Get.find<TransferResourcesPageController>();
        transferController.hasNewBankAccountBeenCreated.value = true;
      }

      CMBottomSheet.simple(
        topWidget: dialogIcon,
        title: dialogTitle,
        description: dialogDescription,
        canPop: false,
        buttonOnPressed: _returnToSourceRoute,
      ).show();
    }
  }

  void _returnToSourceRoute() => Get.until((route) => route.settings.name == sourceRoute);
}
