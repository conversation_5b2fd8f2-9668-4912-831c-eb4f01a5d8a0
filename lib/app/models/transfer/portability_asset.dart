import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'portability_asset.g.dart';

@JsonSerializable()
class PortabilityAsset {
  final int? id;
  final String? type;
  final String? name;
  final double? availableQuantity;
  double? selectedQuantity;
  bool isSelected;
  @JsonKey(includeFromJson: false, includeToJson: false)
  FocusNode? focusNode;

  PortabilityAsset({
    this.id,
    this.type,
    this.name,
    this.availableQuantity,
    this.selectedQuantity,
    this.isSelected = false,
    this.focusNode,
  });

  factory PortabilityAsset.fromJson(Map<String, dynamic> json) => _$PortabilityAssetFromJson(json);
  Map<String, dynamic> toJson() => _$PortabilityAssetToJson(this);
}
