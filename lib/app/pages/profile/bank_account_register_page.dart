import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../api/bancos/banco.dart';
import '../../config/app_theme.dart';
import '../../controllers/bank_account_registration_controller.dart';
import '../../utils/extensions.dart';
import '../../utils/formatters.dart';
import '../../utils/ui_utils.dart';
import '../../utils/validators.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/scaffold/top_bar.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/input/labeled_checkbox.dart';
import '../../widgets/input/input.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/input/dropdown_search.dart';
import '../../widgets/loading/skeleton.dart';

class BankAccountRegisterPage extends StatelessWidget {
  static const routeName = '/perfil/nova-conta-bancaria';

  const BankAccountRegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX(
      init: BankAccountRegistrationController(),
      builder: (controller) {
        return AppScaffold(
          appBar: const TopBar(title: "Cadastrar Conta Bancária", whiteBackground: false),
          resizeToAvoidBottomInset: true,
          body: CMBody(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: AppTheme.pagePadding,
              child: Form(
                key: controller.accountFormKey,
                autovalidateMode: controller.accountFormValidateMode.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Dados bancários
                    const Padding(
                      padding: EdgeInsets.only(bottom: 16),
                      child: Text('Insira os dados da conta bancária que deseja cadastrar:', style: AppTheme.regular12White),
                    ),
                    if (controller.isLoadingBanks.isTrue)
                      Padding(
                        padding: const EdgeInsets.only(top: 10, bottom: 20),
                        child: Skeleton(
                          height: 45,
                          borderRadius: BorderRadius.circular(5),
                        ),
                      )
                    else
                      DropdownSearch<Banco>(
                        hintText: 'Escolha seu banco',
                        initialItem: controller.selectedBank.value,
                        listItemDisplay: (bank) => bank.name,
                        onItemSelected: (bank) => controller.selectedBank.value = bank,
                        onSearch: (text) async => controller.bankList.where((bank) => bank.name.matchToSearch(text)).toList(),
                        prefixIcon: iconify('assets/icons/field_bank.svg', size: 18, color: AppTheme.orangeColor),
                        nullable: false,
                      ),
                    Input(
                      hintText: 'Agência',
                      type: InputType.bankBranchCode,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) => validateMinimumLength(value, 3),
                      prefixIconPath: 'assets/icons/apps_rounded.svg',
                      onChanged: (value) => controller.accountData.update((data) => data?.agencia = value),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 16),
                            child: Input(
                              hintText: 'Conta',
                              type: InputType.bankAccountNumber,
                              keyboardType: TextInputType.phone,
                              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                              validator: (value) => validateMinimumLength(value, 3),
                              prefixIconPath: 'assets/icons/apps_rounded.svg',
                              onChanged: (value) => controller.accountData.update((data) => data?.conta = value.padLeft(6, '0')),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Input(
                            hintText: 'Dígito',
                            type: InputType.bankAccountNumberDigit,
                            keyboardType: TextInputType.phone,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            validator: (value) => validateLength(value, 1),
                            prefixIconPath: 'assets/icons/apps_rounded.svg',
                            onChanged: (value) => controller.accountData.update((data) => data?.digito = value),
                          ),
                        ),
                      ],
                    ),

                    // Conta conjunta
                    LabeledCheckbox(
                      value: controller.accountData.value.conjunta?.toLowerCase() == 'true',
                      onChanged: (value) {
                        controller.accountData.update((data) => data?.conjunta = value.toString());
                        if (value == false) {
                          controller.accountData.update((data) {
                            data?.nomeConjunta = null;
                            data?.cpfConjunta = null;
                          });
                        }
                      },
                      textLabel: 'A conta bancária a ser cadastrada é conjunta',
                    ),
                    const Divider(color: AppTheme.blueColor),
                    AnimatedCrossFade(
                      duration: const Duration(milliseconds: 300),
                      crossFadeState: controller.isJointAccount ? CrossFadeState.showFirst : CrossFadeState.showSecond,
                      firstChild: Container(
                        padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 80),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(bottom: 16),
                              child: Text(
                                'Complete com as informações do Cotitular:',
                                style: AppTheme.regular11White,
                                softWrap: false,
                                overflow: TextOverflow.fade,
                              ),
                            ),
                            Input(
                              hintText: 'CPF do Cotitular',
                              keyboardType: TextInputType.phone,
                              inputFormatters: cpfInputFormatter,
                              validator: controller.isJointAccount ? controller.validateJointAccountCpf : null,
                              prefixIconPath: 'assets/icons/apps_rounded.svg',
                              onChanged: (value) => controller.accountData.update((data) => data?.cpfConjunta = value),
                            ),
                            Input(
                              hintText: 'Nome do Cotitular',
                              textCapitalization: TextCapitalization.words,
                              inputFormatters: lettersInputFormatter,
                              validator: controller.isJointAccount ? (value) => validateMinimumLength(value, 3) : null,
                              prefixIcon: iconify(
                                Icons.person_outline,
                                color: AppTheme.orangeColor,
                                size: 22,
                              ),
                              onChanged: (value) => controller.accountData.update((data) => data?.nomeConjunta = value),
                            ),
                          ],
                        ),
                      ),
                      secondChild: const SizedBox(),
                    ),
                  ],
                ),
              ),
            ),
          ),
          bottomNavigationBar: Container(
              color: const Color(0xFF17191E),
              padding: const EdgeInsets.fromLTRB(25, 0, 25, 25),
              child: Button.elevated(
                text: controller.isSubmittingAccountData.isTrue ? 'Cadastrando...' : 'Cadastrar',
                disabled: controller.isSubmittingAccountData.isTrue,
                margin: const EdgeInsets.symmetric(vertical: 10),
                onPressed: controller.submitAccountData,
              )),
        );
      },
    );
  }
}
