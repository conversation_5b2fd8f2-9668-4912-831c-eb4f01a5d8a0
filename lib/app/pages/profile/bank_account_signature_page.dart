import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../controllers/bank_account_registration_controller.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/scaffold/top_bar.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/info/product_summary.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/info/vertical_field.dart';
import '../../widgets/input/secret_input.dart';
import 'secret_change_page.dart';

class BankAccountSignaturePage extends StatelessWidget {
  const BankAccountSignaturePage({super.key});
  static const routeName = '/perfil/nova-conta-bancaria/assinatura';

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const TopBar(title: 'Cadastrar conta bancária'),
      extendBody: true,
      body: GetX<BankAccountRegistrationController>(
        builder: (controller) {
          return CMBody(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: AppTheme.horizontalPadding,
              child: Form(
                key: controller.signatureFormKey,
                child: Column(
                  children: [
                    // Resumo
                    ProductSummary(
                      fields: [
                        VerticalField('Banco', controller.bankName, widthFactor: 1),
                        VerticalField('Agência', controller.accountData.value.agencia),
                        VerticalField('Conta', controller.accountData.value.conta),
                        VerticalField('Dígito', controller.accountData.value.digito),
                      ],
                    ),

                    // Assinatura
                    SecretInput(
                      label: 'Insira sua assinatura eletrônica para confirmar o cadastramento da conta',
                      hintText: 'Assinatura eletrônica',
                      secretValue: controller.signature,
                    ),

                    // Botão para confirmar
                    const SizedBox(height: 20),
                    Button.elevated(
                      text: 'Confirmar',
                      onPressed: controller.createBankAccount,
                    ),

                    // Botão para atualizar assinatura
                    GestureDetector(
                      onTap: () => Get.toNamed(SecretChangePage.routeName, arguments: 'assinatura'),
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Text(
                          'Esqueceu sua assinatura?',
                          style: AppTheme.regular11White,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
