import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../api/usuario/usuario_api.dart';
import '../../config/app_theme.dart';
import '../../errors/error_handlers.dart';
import '../../utils/validators.dart';
import '../../widgets/modal/cm_dialog.dart';
import '../../widgets/input/input.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../../widgets/input/secret_input.dart';
import '../auth/password_recovery/password_recovery_page.dart';
import '../auth/token_verification.dart';

class SecretChangePage extends StatefulWidget {
  static const routeName = '/profile/secret-change';

  @override
  State<SecretChangePage> createState() => _SecretChangePageState();
}

class _SecretChangePageState extends State<SecretChangePage> {
  final _formKey = GlobalKey<FormState>();
  final _newSecretKey = GlobalKey<InputState>();
  final _currentPasswordKey = GlobalKey<InputState>();
  final _usuarioApi = Get.find<UsuarioApi>();

  final _newSecret = TextEditingController();
  final _confirmNewSecret = TextEditingController();
  final _currentPasswordController = TextEditingController();

  final _currentPassword = <List<int>>[];
  bool _isLoading = false;

  String? get _secret => ModalRoute.of(context)?.settings.arguments as String?;

  Future<void> _submit({bool shouldBypassValidation = false}) async {
    if (_formKey.currentState?.validate() == true) {
      try {
        setState(() => _isLoading = true);
        if (_secret case 'senha' || 'assinatura') {
          if (!shouldBypassValidation) {
            await _requestSecretChangeValidation();
          }

          final token = await Get.toNamed(
            TokenVerificationPage.routeName,
            arguments: TokenVerificationPageArguments(
              contentTitle: 'Alterar $_secret',
              resendToken: _requestSecretChangeValidation,
            ),
          );

          if (token is! String || token.length < 6) {
            return;
          }

          await _submitSecretChange(token: token);
        } else {
          CMDialog.warning(
            description: 'Não é possível alterar "$_secret".',
            title: 'Alerta',
            showCancelButton: false,
            confirmButtonText: 'Entendi',
          ).show();
          return;
        }
        CMBottomSheet.simple(
          topWidget: Image.asset('assets/icons/dialog/hand_on_password_lock.png'),
          title: '${_secret?.capitalize} atualizada',
          description: 'Sua ${_secret?.capitalize} foi atualizada com sucesso.',
        ).show();
      } on DioException catch (error) {
        String errorCode = error.response?.data.toString() ?? error.toString();
        // error:
        // "01": "[Assinaturas/Senhas] não conferem",
        // "02": "Sua [assinatura/senha] já foi usada anteriormente",
        // "03": "Sua senha informada está incorreta",
        // "04": "Sua [assinatura/senha] não pode conter números sequenciais",
        // "05": "Digite 6 caracteres no campo de sua senha atual!"
        // "06": "Token inválido!"
        // "07": "Número de tentativas excedido. Conta bloqueada temporariamente."
        String errorDescription = await getErrorDescription(error);
        if (errorCode.contains("error.01") || errorCode.contains("error.02") || errorCode.contains("error.04")) {
          _currentPasswordKey.currentState?.updateError(null);
          _newSecret.clear();
          _confirmNewSecret.clear();
          _newSecretKey.currentState?.updateError(errorDescription);
        } else if (errorCode.contains("error.03") || errorCode.contains("error.05")) {
          _newSecretKey.currentState?.updateError(null);
          _currentPasswordController.clear(); // Limpa o texto do input
          _currentPassword.clear(); // Limpa o valor que é enviado a API
          _currentPasswordKey.currentState?.updateError(errorDescription);
        }
        await onError(error);
        // se o erro é de token inválido, retorna para tela de digitação do token sem enviar um novo
        if (errorCode.contains("error.06")) {
          await _submit(shouldBypassValidation: true);
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _requestSecretChangeValidation() async {
    if (_secret == 'senha') {
      await _usuarioApi.usuarioAlterarSenhaValidar(
        UsuarioAlterarSenhaValidarRequest(
          newPassword: _newSecret.text,
        ),
      );
    } else {
      await _usuarioApi.usuarioAlterarAssinaturaValidar(
        UsuarioAlterarAssinaturaValidarRequest(
          newSignature: _newSecret.text,
        ),
      );
    }
  }

  Future<void> _submitSecretChange({required String token}) async {
    if (_secret == 'senha') {
      await _usuarioApi.usuarioAlterarSenha(
        UsuarioAlterarSenhaRequest(
          newPassword: _newSecret.text,
          currentPassword: _currentPassword,
          token: token,
        ),
      );
    } else {
      await _usuarioApi.usuarioAlterarAssinatura(
        UsuarioAlterarAssinaturaRequest(
          newSignature: _newSecret.text,
          password: _currentPassword,
          token: token,
        ),
      );
    }
  }

  List<Widget> get _text {
    return [
      Padding(
        padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
        child: Text(
          'Defina sua $_secret a partir dos parâmetros abaixo:',
          style: AppTheme.regular11White,
        ),
      ),
      Text(
        '1. Somente números \n2. Conter exatamente 6 caracteres \n3. Não usar números sequenciais (123456 ou 111222) \n4. Não ser igual as 6 últimas ${_secret}s',
        style: AppTheme.regular11White,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: TopBar(title: 'Alterar $_secret'.capitalize),
      extendBody: true,
      body: CMBody(
        child: Padding(
          padding: const EdgeInsets.all(25.0),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ..._text,
                  const SizedBox(height: 20),
                  Input(
                    key: _newSecretKey,
                    hintText: 'Nova $_secret',
                    controller: _newSecret,
                    type: InputType.secret,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIconPath: 'assets/icons/field_padlock.svg',
                    obscureText: true,
                    autofocus: _newSecret.text.isEmpty,
                    validator: validatePassword,
                  ),
                  const SizedBox(height: 20),
                  Input(
                    hintText: 'Confirmar nova $_secret',
                    controller: _confirmNewSecret,
                    type: InputType.secret,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIconPath: 'assets/icons/field_padlock.svg',
                    obscureText: true,
                    validator: (_) => validatePasswordConfirmation(_newSecret.text, _confirmNewSecret.text),
                  ),
                  const SizedBox(height: 20),
                  SecretInput(
                    key: _currentPasswordKey,
                    controller: _currentPasswordController,
                    hintText: 'Senha atual',
                    secretValue: _currentPassword,
                  ),
                  Button.elevated(
                    text: _isLoading ? 'Atualizando...' : 'Atualizar',
                    onPressed: _submit,
                    disabled: _isLoading,
                    margin: const EdgeInsets.symmetric(vertical: 10),
                  ),
                  Center(
                    child: GestureDetector(
                      child: const Padding(
                        padding: EdgeInsets.only(top: 20),
                        child: Text(
                          'Esqueci minha senha',
                          style: AppTheme.regular11White,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      onTap: () => Get.toNamed(PasswordRecoveryPage.routeName),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
