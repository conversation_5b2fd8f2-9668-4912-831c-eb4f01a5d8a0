import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../api/bancos/bancos_api.dart';
import '../../api/usuario/usuario.dart';
import '../../config/app_theme.dart';
import '../../controllers/user_controller.dart';
import '../../errors/error_handlers.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/card/bank_card.dart';
import '../../widgets/loading/loading.dart';
import '../../widgets/info/product_summary.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/modal/cm_bottom_sheet.dart';
import '../../widgets/info/vertical_field.dart';
import '../auth/electronic_signature.dart';
import '../profile/bank_account_register_page.dart';

class BankAccountsPage extends StatefulWidget {
  static const routeName = '/profile/bank-accounts';

  const BankAccountsPage({super.key});

  @override
  State<StatefulWidget> createState() => _BankAccountsPageState();
}

class _BankAccountsPageState extends State<BankAccountsPage> {
  final _bancoApi = Get.find<BancosApi>();
  final _userController = Get.find<UserController>();
  List<ReferenciaBancaria> referenciasBancarias = [];
  late final Usuario? usuario;
  DataState state = DataState.loading;

  @override
  void initState() {
    super.initState();
    usuario = _userController.usuario;
    _getAccounts();
  }

  Widget get _emptyState {
    return CMBody(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 200,
            child: Text(
              'Você ainda não possui nenhuma conta cadastrada.',
              textAlign: TextAlign.center,
              style: AppTheme.defaultText,
            ),
          ),
          const SizedBox(height: 24),
          Button(
            text: 'Cadastrar',
            width: 200,
            buttonType: ButtonType.elevated,
            onPressed: _register,
          )
        ],
      ),
    );
  }

  Widget get _loadingState {
    return const Loading(
      description: 'Carregando contas bancárias...',
    );
  }

  Widget get _errorState {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          width: 200,
          child: Text(
            'Não foi possível obter suas contas bancárias.',
            textAlign: TextAlign.center,
            style: AppTheme.defaultText,
          ),
        ),
        const SizedBox(height: 16),
        Button(
          text: 'Tentar novamente',
          width: 200,
          buttonType: ButtonType.elevated,
          onPressed: () => _getAccounts(),
        )
      ],
    );
  }

  Widget get _fulfilledState {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 20),
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: referenciasBancarias.length,
        itemBuilder: (BuildContext ctx, int index) {
          return BankCard(
            title: '${referenciasBancarias[index].bankCode.toString()} - ${referenciasBancarias[index].bankName}',
            contentAlignment: referenciasBancarias[index].coownerCpf != null ? WrapAlignment.spaceBetween : WrapAlignment.start,
            content: [
              [
                CMBankCardContent("Agência", referenciasBancarias[index].branch.toString()),
                CMBankCardContent("Conta", "${referenciasBancarias[index].account}-${referenciasBancarias[index].digit}"),
                if (referenciasBancarias[index].coownerCpf != null)
                  CMBankCardContent(
                    "CPF do Cotitular",
                    MaskTextInputFormatter(mask: '###.###.###-##', initialText: referenciasBancarias[index].coownerCpf).getMaskedText(),
                  )
              ],
              if (referenciasBancarias[index].coownerName != null)
                [
                  CMBankCardContent("Nome do Cotitular", referenciasBancarias[index].coownerName!),
                ]
            ],
            onDelete: () {
              CMBottomSheet.choice(
                title: 'Remover conta bancária',
                description: 'Tem certeza que deseja remover a conta bancária?',
                topButtonText: 'Sim',
                topButtonOnPressed: () async => WidgetsBinding.instance.addPostFrameCallback((_) async {
                  try {
                    final signature = await Get.toNamed(
                      ElectronicSignaturePage.routeName,
                      arguments: {
                        'title': 'Remover conta bancária',
                        'data': ProductSummary(
                          fields: [
                            VerticalField('Banco', referenciasBancarias[index].bankName, widthFactor: 1),
                            VerticalField('Agência', referenciasBancarias[index].branch.toString()),
                            VerticalField('Conta', referenciasBancarias[index].account),
                            VerticalField('Dígito', referenciasBancarias[index].digit),
                          ],
                        ),
                      },
                    );
                    if (signature is! List<List<int>>) return;
                    await _bancoApi.pessoaRemoverDadosBancarios(
                      PessoaRemoverDadosBancariosRequest(
                        codigo: referenciasBancarias[index].id.toString(),
                        assinatura: signature,
                      ),
                    );
                    CMBottomSheet.simple(
                      topWidget: Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Image.asset('assets/icons/dialog/delete_alert.png'),
                      ),
                      title: 'Sucesso!',
                      description: 'Conta removida com sucesso!',
                    ).show();
                    _getAccounts();
                  } on DioException catch (error) {
                    return onError(error);
                  }
                }),
                bottomButtonText: 'Não',
                bottomButtonOnPressed: Get.back,
                topWidget: Image.asset(
                  'assets/icons/dialog/bank_account_registration.png',
                ),
              ).show();
            },
          );
        },
      ),
    );
  }

  void _getAccounts() async {
    setState(() => state = DataState.loading);
    if (usuario != null) {
      List<ReferenciaBancaria> referenciaBancarias = await _bancoApi.pessoaReferenciaBancaria();

      setState(() => state = referenciaBancarias.isEmpty ? DataState.empty : DataState.fulfilled);

      setState(() => referenciasBancarias = referenciaBancarias);
      return;
    }

    setState(() => state = DataState.error);
    return null;
  }

  void _register() async {
    await Navigator.of(context).pushNamed(BankAccountRegisterPage.routeName);
    _getAccounts();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: TopBar(title: 'Contas Bancárias', icon: 'assets/icons/action_add.svg', iconOnPressed: _register),
      extendBody: true,
      body: CMBody(
        child: (() {
          switch (state) {
            case DataState.empty:
              return _emptyState;
            case DataState.loading:
              return _loadingState;
            case DataState.fulfilled:
              return _fulfilledState;
            case DataState.error:
              return _errorState;
          }
        }()),
      ),
    );
  }
}
