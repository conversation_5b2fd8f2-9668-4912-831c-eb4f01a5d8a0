import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../config/app_theme.dart';
import '../../controllers/user_controller.dart';
import '../../utils/countdown.dart';
import '../../utils/formatters.dart';
import '../../widgets/body/cm_body.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/buttons/button.dart';
import '../../widgets/scaffold/top_bar.dart';

class TokenVerificationPageArguments {
  final String? pageTitle;
  final String? contentTitle;

  /// Padrão: Enviamos um código de confirmação de [tokenLength] dígitos para o e-mail `UserController.usuarioDados.email`.
  final String? description;
  final String? label;

  /// Padrão: `(token) async => Get.back(result: token)`.
  final Future<void> Function(String token)? submitToken;
  final Future<void> Function()? resendToken;
  final int tokenLength;

  /// Padrão: [CMBody.defaultGradient].
  final Color? bodyBackgroundColor;

  const TokenVerificationPageArguments({
    this.pageTitle = 'Validar token',
    this.contentTitle,
    this.description,
    this.label = 'Insira o código no campo abaixo.',
    this.submitToken,
    this.resendToken,
    this.tokenLength = 6,
    this.bodyBackgroundColor,
  });
}

class TokenVerificationPage extends StatelessWidget {
  late final TokenVerificationPageArguments _arguments;

  TokenVerificationPage({super.key}) {
    if (Get.arguments case TokenVerificationPageArguments arguments) {
      _arguments = arguments;
    } else {
      _arguments = const TokenVerificationPageArguments();
    }
  }

  static const routeName = '/verificacao-token';

  String? get _userEmail => Get.find<UserController>().usuarioDados?.email;
  String get _defaultDescription => 'Enviamos um código de ${_arguments.tokenLength} dígitos para ${_userEmail != null ? 'o e-mail $_userEmail' : 'seu e-mail'}.';

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: _arguments.pageTitle != null ? TopBar(title: _arguments.pageTitle) : null,
      body: CMBody(
        customGradient: _arguments.bodyBackgroundColor != null
            ? LinearGradient(
                colors: [_arguments.bodyBackgroundColor!, _arguments.bodyBackgroundColor!],
              )
            : null,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(25, 50, 25, 0),
          child: TokenVerificationForm(
            title: _arguments.contentTitle,
            description: _arguments.description ?? _defaultDescription,
            label: _arguments.label,
            submitToken: _arguments.submitToken ?? (token) async => Get.back(result: token),
            resendToken: _arguments.resendToken,
            tokenLength: _arguments.tokenLength,
          ),
        ),
      ),
    );
  }
}

class TokenVerificationForm extends StatefulWidget {
  final String? title;
  final String? description;
  final String? label;

  /// Quando informado, botão para confirmação é mostrado e função é chamada ao clicar.
  final Future<void> Function(String token)? submitToken;

  /// Quando informado, botão para reenvio é mostrado e função é chamada ao clicar.
  final Future<void> Function()? resendToken;
  final void Function(String token)? onChanged;
  final GlobalKey<FormState>? formKey;
  final AutovalidateMode? formAutovalidateMode;
  final int tokenLength;

  const TokenVerificationForm({
    super.key,
    this.title,
    this.description,
    this.label,
    this.submitToken,
    this.resendToken,
    this.onChanged,
    this.formKey,
    this.formAutovalidateMode,
    this.tokenLength = 6,
  });

  @override
  State<TokenVerificationForm> createState() => _TokenVerificationFormState();
}

class _TokenVerificationFormState extends State<TokenVerificationForm> {
  late final GlobalKey<FormState> _formKey = widget.formKey ?? GlobalKey<FormState>();
  late AutovalidateMode _autoValidateMode = widget.formAutovalidateMode ?? AutovalidateMode.disabled;
  var _token = '';
  late final countdown = Countdown(
    30,
    onTick: (_) => setState(() {}),
  );
  String get _resendText => countdown.appendRemainingSeconds('Reenviar código');

  @override
  void initState() {
    super.initState();
    if (widget.resendToken != null) countdown.start();
  }

  void _onPinChanged(String value) {
    setState(() => _token = value);
    widget.onChanged?.call(_token);
  }

  String? _validatePin(String? value) {
    if ((value ?? '').numericOnly().length < widget.tokenLength) return 'Informe os ${widget.tokenLength} dígitos do código.';
    return null;
  }

  bool _validateForm() {
    if (_autoValidateMode != AutovalidateMode.always) {
      setState(() => _autoValidateMode = AutovalidateMode.always);
    }
    return _formKey.currentState?.validate() == true;
  }

  Future<void> _submit() async {
    if (_validateForm() == false) return;
    await widget.submitToken?.call(_token);
  }

  Future<void> _resend() async {
    await widget.resendToken?.call();
    countdown.start();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título
          if (widget.title case String title) ...[
            Text(title, style: AppTheme.semi24White),
            const SizedBox(height: 30),
          ],

          // Descrição
          if (widget.description case String description) ...[
            Text(description, style: AppTheme.regular14White),
            const SizedBox(height: 20),
          ],

          // Label
          if (widget.label case String label) ...[
            Text(label, style: AppTheme.regular14White),
            const SizedBox(height: 10),
          ],

          // Tema para diálogo de colar código
          Theme(
            data: Theme.of(context).copyWith(
              textTheme: const TextTheme(
                labelLarge: AppTheme.medium16Black,
              ),
              dialogTheme: const DialogTheme(
                titleTextStyle: AppTheme.bold18Black,
              ),
            ),
            // Campo para token
            child: PinCodeTextField(
              dialogConfig: DialogConfig(
                dialogTitle: 'Colar',
                dialogContent: 'Deseja colar este código ',
                affirmativeText: 'Sim',
                negativeText: 'Cancelar',
              ),
              appContext: context,
              length: widget.tokenLength,
              onChanged: _onPinChanged,
              keyboardType: TextInputType.number,
              validator: _validatePin,
              errorTextSpace: 25,
              textStyle: AppTheme.bold24Black,
              animationType: AnimationType.none,
              inputFormatters: numberInputFormatter,
              showCursor: false,
              enableActiveFill: true,
              autoFocus: true,
              autovalidateMode: _autoValidateMode,
              pinTheme: PinTheme(
                shape: PinCodeFieldShape.box,
                borderRadius: BorderRadius.circular(6),
                fieldWidth: 50,
                fieldHeight: 65,
                activeFillColor: AppTheme.whiteColor,
                activeColor: Colors.transparent,
                inactiveFillColor: AppTheme.whiteColor,
                inactiveColor: Colors.transparent,
                selectedFillColor: AppTheme.whiteColor,
                selectedColor: AppTheme.darkOrangeColor,
                errorBorderColor: Colors.transparent,
              ),
            ),
          ),

          // Confirmar
          if (widget.submitToken != null)
            Button.elevated(
              margin: const EdgeInsets.only(top: 20),
              text: 'Confirmar',
              onPressed: _submit,
              disabled: _token.length < widget.tokenLength,
            ),

          // Reenviar
          if (widget.resendToken != null)
            Button.text(
              margin: const EdgeInsets.only(top: 20),
              text: _resendText,
              onPressed: _resend,
              disabled: countdown.isActive,
              textStyle: AppTheme.medium14Orange,
            ),
        ],
      ),
    );
  }
}
