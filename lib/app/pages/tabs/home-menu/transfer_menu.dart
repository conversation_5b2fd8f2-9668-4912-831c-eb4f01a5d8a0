import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../config/app_theme.dart';
import '../../../controllers/transfers/portability_history_controller.dart';
import '../../../widgets/loading/skeleton.dart';
import '../submenu_page.dart';
import '../transfers/deposit_information_page.dart';
import '../transfers/portability_request_page.dart';
import '../transfers/transfer_custody_history_page.dart';
import '../transfers/transfer_custody_page.dart';
import '../transfers/transfer_resources_page.dart';

void openTransferMenu() {
  // Inicia o controller para carregar obter o total de pendências
  final portabilityController = Get.put(PortabilityHistoryController());

  Get.toNamed(
    SubmenuPage.routeName,
    preventDuplicates: false,
    arguments: SubmenuPageArguments(
      title: 'Transferências',
      items: [
        SubmenuPageItem(
          text: 'Depósitos',
          onTap: () => Get.toNamed(DepositInformationPage.routeName),
          badge: 'Pix',
        ),
        SubmenuPageItem(
          text: 'Recursos',
          onTap: () => Get.toNamed(TransferResourcesPage.routeName),
        ),
        // TODO: remover após conclusão de portabilidade
        SubmenuPageItem(
          text: 'Custódia',
          onTap: () => Get.toNamed(TransferCustodyPage.routeName),
        ),
        SubmenuPageItem(
          text: 'Portabilidade (STVM)',
          onTap: () => Get.toNamed(PortabilityRequestPage.routeName),
        ),
        SubmenuPageItem(
          text: 'Pedidos de Portabilidade',
          subtitleWidget: Obx(
            () {
              // Indicador de carregamento
              if (portabilityController.isLoading.value) {
                return const Skeleton(width: 80, height: 16, borderRadius: BorderRadius.all(Radius.circular(4)));
              }

              // Pendências
              final count = portabilityController.pendingRequestsCount;
              if (count <= 0) return const SizedBox.shrink();
              final subtitle = count == 1 ? '1 pendência' : '$count pendências';
              return Text(subtitle, style: AppTheme.regular12Orange);
            },
          ),
          onTap: () => Get.toNamed(TransferCustodyHistoryPage.routeName),
        ),
      ],
    ),
  );
}
