import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/transfers/portability_to_request_controller.dart';
import '../../../../widgets/card/portability_exchange_card.dart';
import '../../../../widgets/list/asset_quantity_selection_list.dart';
import '../../../../widgets/scaffold/portability_request_scaffold.dart';
import '../portability_request_page.dart';

class PortabilityRequestAssetSelectionPage extends StatelessWidget {
  static const routeName = '${PortabilityRequestPage.routeName}/ativos';

  const PortabilityRequestAssetSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX(
      init: PortabilityToRequestController(),
      builder: (controller) {
        return PortabilityRequestScaffold(
          title: 'Portabilidade CM Capital',
          subtitle: 'Enviar recursos',
          topText: 'Selecione os investimentos e as quantidades para seguir com a portabilidade.',
          onGetData: controller.fetchAsstes,
          bottomButtonText: 'Continuar',
          bottomButtonOnPressed: controller.canSubmitAssetSelection ? controller.onAssetSelectionSubmit : null,
          checkboxText: 'Aceito a portabilidade parcial, conforme disponibilidade dos ativos selecionados.',
          isChecked: controller.isPartialPortabilityAccepted,
          onCheckboxChanged: controller.onPartialPortabilityAccepted,
          columnChildren: [
            // Corretora de destino
            const SizedBox(height: 16),
            PortabilityExchangeCard(
              name: controller.selectedExchange.value?.name ?? 'BTG Pactual',
              isOrigin: false,
            ),

            // Seleção de ativos e quantidades
            const SizedBox(height: 16),
            AssetQuantitySelectionList(assets: controller.assets, onAssetChanged: controller.assets.refresh),
          ],
        );
      },
    );
  }
}
