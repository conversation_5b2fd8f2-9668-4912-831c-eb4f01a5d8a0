import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../config/app_theme.dart';
import '../../../../controllers/transfers/portability_to_request_controller.dart';
import '../../../../models/transfer/exchange.dart';
import '../../../../utils/formatters.dart';
import '../../../../utils/ui_utils.dart';
import '../../../../utils/validators.dart';
import '../../../../widgets/input/dropdown_search.dart';
import '../../../../widgets/input/input.dart';
import '../../../../widgets/scaffold/portability_request_scaffold.dart';
import '../portability_request_page.dart';

class PortabilityRequestExchangeSelectionPage extends StatelessWidget {
  static const routeName = '${PortabilityRequestPage.routeName}/corretora';

  const PortabilityRequestExchangeSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX(
      init: PortabilityToRequestController(),
      builder: (controller) {
        return PortabilityRequestScaffold(
          title: 'Portabilidade CM Capital',
          subtitle: 'Enviar recursos',
          topText: 'Para qual corretora deseja realizar a portabilidade dos seus investimentos?',
          onGetData: controller.fetchExchanges,
          bottomButtonText: controller.exchanges.isEmpty ? null : 'Continuar',
          bottomButtonOnPressed: controller.canSubmitExchangeSelection ? controller.onExchangeSubmit : null,
          formKey: controller.exchangeFormKey,
          formAutovalidateMode: controller.exchangeFormKeyAutovalidateMode.value,
          columnChildren: [
            Column(
              children: [
                // Corretora
                Obx(
                  () => DropdownSearch<Exchange>(
                    padding: const EdgeInsets.only(top: 24),
                    label: 'Selecione a corretora de destino',
                    hintText: 'Selecione uma opção',
                    prefixIcon: iconify('assets/icons/fields_agency.svg', size: 18, color: AppTheme.orangeColor),
                    suffixIcon: iconify(Icons.search, size: 30, color: AppTheme.orangeColor),
                    initialItem: controller.selectedExchange.value,
                    onSearch: controller.filterExchanges,
                    listItemDisplay: (exchange) => exchange.name ?? 'Nome indefinido',
                    onItemSelected: (exchange) => controller.selectedExchange.value = exchange,
                    maxLines: 1,
                    nullable: false,
                    validator: validateRequiredField,
                  ),
                ),

                // Sinacor
                Input(
                  padding: const EdgeInsets.only(top: 24),
                  label: 'Código Sinacor',
                  hintText: 'Digite o código Sinacor',
                  keyboardType: TextInputType.number,
                  inputFormatters: numberInputFormatter,
                  prefixIcon: iconify(Icons.person_outline, size: 24, color: AppTheme.orangeColor),
                  onChanged: (text) => controller.sinacorCode.value = text,
                  validator: validateRequiredField,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
