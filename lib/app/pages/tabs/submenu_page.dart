import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../config/app_theme.dart';
import '../../widgets/info/badge.dart';
import '../../widgets/scaffold/app_scaffold.dart';
import '../../widgets/icons/arrow_icon.dart';

class SubmenuPage extends StatelessWidget {
  static const routeName = '/submenu';

  const SubmenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments as SubmenuPageArguments;

    return AppScaffold(
      appBar: arguments.customAppBar ?? TopBar(title: arguments.title),
      extendBody: true,
      body: CMBody(
        child: SubmenuList(
          headerWidget: arguments.headerWidget,
          items: arguments.items,
        ),
      ),
    );
  }
}

class SubmenuList extends StatelessWidget {
  final Widget? headerWidget;
  final List<SubmenuPageItem>? items;

  const SubmenuList({super.key, this.headerWidget, this.items});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + Get.mediaQuery.padding.bottom),
        child: Column(
          children: [
            // Cabeçalho
            if (headerWidget != null) headerWidget!,

            // Lista de itens
            if (items?.isNotEmpty ?? false)
              ...items!.map(
                (item) => InkWell(
                  // Ação
                  onTap: item.disabled || item.onTap == null ? null : () => item.onTap!(),

                  // Card
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.whiteColor.withValues(alpha: 0.2), width: 1),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Row(
                      children: [
                        // Ícone opcional
                        if (item.iconPath != null && item.iconPath!.isNotEmpty)
                          Container(
                            margin: const EdgeInsets.only(right: 10),
                            child: SvgPicture.asset(
                              item.iconPath!,
                              colorFilter: const ColorFilter.mode(AppTheme.whiteColor, BlendMode.srcIn),
                              height: item.iconHeight,
                            ),
                          ),

                        // Texto e badge
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 5),
                            child: Row(
                              children: [
                                // Texto e subtítulo
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item.text ?? '',
                                        style: AppTheme.regular16White.copyWith(height: 1),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                      if (item.subtitleWidget != null) ...[
                                        const SizedBox(height: 4),
                                        item.subtitleWidget!,
                                      ],
                                    ],
                                  ),
                                ),

                                // Badge
                                if (item.badge != null)
                                  CMBadge(
                                    margin: const EdgeInsets.symmetric(horizontal: 20),
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    borderRadius: 4,
                                    text: item.badge!,
                                    textStyle: AppTheme.bold11White,
                                    isTextUpperCase: true,
                                  ),
                              ],
                            ),
                          ),
                        ),

                        // Ícone da seta
                        const ArrowForwardIcon(),
                      ],
                    ),
                  ),
                ),
              ),
            // Espaçamento
            SizedBox(height: Get.mediaQuery.viewPadding.bottom),
          ],
        ),
      ),
    );
  }
}

class SubmenuPageArguments {
  final String title;
  final List<SubmenuPageItem>? items;
  final Widget? headerWidget;
  final TopBar? customAppBar;

  SubmenuPageArguments({
    this.title = '',
    this.items,
    this.headerWidget,
    this.customAppBar,
  });
}

class SubmenuPageItem {
  final String? text;
  final Widget? subtitleWidget;
  final String? iconPath;
  final double? iconHeight;
  final Function? onTap;
  final bool disabled;
  final String? badge;

  SubmenuPageItem({
    this.text,
    this.subtitleWidget,
    this.iconPath,
    this.iconHeight,
    this.onTap,
    this.disabled = false,
    this.badge,
  });
}
