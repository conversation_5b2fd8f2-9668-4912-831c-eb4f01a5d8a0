import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';

import 'app/api/analyst_trade/analyst_trade_api.dart';
import 'app/api/bancos/bancos_api.dart';
import 'app/api/banner_offer/banner_offer_api.dart';
import 'app/api/cm_request.dart';
import 'app/api/educational/educational_api.dart';
import 'app/api/extrato/extrato_api.dart';
import 'app/api/investments/credito-privado/credito_privado_api.dart';
import 'app/api/investments/investments_api.dart';
import 'app/api/investments/tesouro-direto/tesouro_direto_api.dart';
import 'app/api/notifications/notification_api.dart';
import 'app/api/online-shop/online_shop_api.dart';
import 'app/api/patrimony/patrimony_api.dart';
import 'app/api/recadastro/recadastro_api.dart';
import 'app/api/rentability/rentability_api.dart';
import 'app/api/shared/shared_api.dart';
import 'app/api/signup/signup_api.dart';
import 'app/api/strategic_trade/strategic_trade_api.dart';
import 'app/api/strategy_trade/strategy_trade_api.dart';
import 'app/api/suitability/suitability_api.dart';
import 'app/api/trade/trade_api.dart';
import 'app/api/trader/trader_api.dart';
import 'app/api/transfers/transfers_api.dart';
import 'app/api/usuario/usuario_api.dart';
import 'app/app_widget.dart';
import 'app/config/app_flavor.dart';
import 'app/config/constants.dart';
import 'app/controllers/balance_controller.dart';
import 'app/controllers/create_new_password_controller.dart';
import 'app/controllers/notifications/notification_controller.dart';
import 'app/controllers/rules_and_parameters_controller.dart';
import 'app/controllers/sensitive_data_controller.dart';
import 'app/controllers/trader_arena_controller.dart';
import 'app/controllers/user_controller.dart';
import 'app/plugins/analytics.dart';
import 'app/plugins/firebase.dart';

void main() async {
  // Obtém flavor definido na variável informada no build e carrega as variáveis de ambiente correspondentes.
  // Homologação: flutter run --flavor hml
  // Produção: flutter run --flavor prod
  AppFlavor.setCurrentFlavor();

  registerSingletons();

  initializeFirebase();

  // Fixa apenas o modo retrato para todo o app
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  Analytics.start();

  initializeDateFormatting();

  runApp(const AppWidget());
}

void registerSingletons() {
  Get.put(CMRequest(Constants.apiUrl));
  Get.put(UserController());
  Get.put(NotificationApi());
  Get.put(NotificationController());
  Get.put(BalanceController());
  Get.put(SensitiveDataController());
  Get.lazyPut(() => TraderArenaController(), fenix: true);
  Get.lazyPut(() => CreateNewPasswordController());
  Get.put(UsuarioApi());
  Get.put(BancosApi());
  Get.put(BannerOfferApi());
  Get.put(SuitabilityApi());
  Get.put(TraderApi());
  Get.put(PatrimonyApi());
  Get.put(ExtratoApi());
  Get.put(EducationalApi());
  Get.put(TransfersApi());
  Get.put(OnlineShopApi());
  Get.put(InvestmentsApi());
  Get.put(RentabilityApi());
  Get.put(RecadastroApi());
  Get.put(TesouroDiretoApi());
  Get.put(CreditoPrivadoApi());
  Get.put(SharedApi());
  Get.put(SignupApi());
  Get.put(TradeApi());
  Get.put(AnalystTradeApi());
  Get.put(StrategyTradeApi());
  Get.put(StrategicTradeApi());
  Get.put(RulesAndParametersController());
}
