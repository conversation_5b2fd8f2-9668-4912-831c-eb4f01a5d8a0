name: cm_capital_app
description: Aplicativo da CM Capital.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 5.15.16+131564

environment:
  sdk: ">=3.6.0 <4.0.0"

dependencies:
  another_xlider: ^3.0.1
  app_links: ^6.3.2
  app_settings: ^5.0.0
  appsflyer_sdk: ^6.12.2
  cached_network_image: ^3.4.1
  caf_document_detector: ^7.0.2
  collection: ^1.17.1
  community_charts_flutter: ^1.0.4
  cupertino_icons: ^1.0.5
  device_info_plus: ^9.0.3
  device_marketing_names: ^0.8.3
  diacritic: ^0.1.4
  dio: ^5.3.2
  encrypt: ^5.0.1
  extended_masked_text: ^2.3.1
  file_picker: ^8.1.4
  firebase_analytics: ^10.7.2
  firebase_core: ^2.24.0
  firebase_remote_config: ^4.3.6
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.4.1
  flutter_exit_app: ^1.1.4
  flutter_html: ^3.0.0-beta.2
  flutter_linkify: ^6.0.0
  flutter_localization: ^0.2.0
  flutter_secure_storage: ^9.2.2
  flutter_svg: ^2.0.7
  get: ^4.6.6
  horizontal_data_table: ^4.3.1
  image_cropper: ^5.0.0
  infinite_scroll_pagination: ^4.0.0
  intl: ^0.19.0
  jailbreak_root_detection: ^1.1.6
  json_annotation: ^4.9.0
  jwt_decoder: ^2.0.1
  local_auth: ^2.2.0
  local_auth_android: ^1.0.39
  local_auth_darwin: ^1.3.1
  mask_text_input_formatter: ^2.5.0
  new_face_liveness: ^4.0.0
  onesignal_flutter: ^3.5.3
  open_filex: ^4.6.0
  package_info_plus: ^4.1.0
  path_provider: ^2.1.5
  permission_handler: ^11.1.0
  pin_code_fields: ^8.0.1
  pointycastle: ^3.7.3
  share_plus: ^7.1.0
  shimmer: ^3.0.0
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  webview_flutter: ^4.10.0
  yaml: ^3.1.2
  youtube_player_flutter: ^9.1.1

dev_dependencies:
  build_runner: ^2.4.14
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^2.0.2
  flutter_native_splash: ^2.3.1
  flutter_test:
    sdk: flutter
  json_serializable: ^6.9.3

dependency_overrides:
  archive: ^3.4.9

flutter_launcher_icons:
  android: "launcher_icon"
  image_path: "assets/app-icon/app-icon.png"
  adaptive_icon_background: "#000d50"
  adaptive_icon_foreground: "assets/app-icon/app-icon.png"
  ios: true

flutter_native_splash:
  image: "assets/app-icon/icon-white-transparent.png"
  background_image: "assets/images/splashscreen_background.png"
  android: true
  ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - pubspec.yaml
    - .fvmrc
    - assets/icons/
    - assets/icons/navbar/
    - assets/icons/signup/
    - assets/icons/investment_types/
    - assets/icons/home_menu/
    - assets/icons/dialog/
    - assets/images/
    - assets/images/onboarding/
    - assets/api/
    - assets/json/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Open Sans
      fonts:
        - asset: fonts/OpenSans-Regular.ttf
          weight: 400
        - asset: fonts/OpenSans-Medium.ttf
          weight: 500
        - asset: fonts/OpenSans-SemiBold.ttf
          weight: 600
        - asset: fonts/OpenSans-Bold.ttf
          weight: 700
        - asset: fonts/OpenSans-ExtraBold.ttf
          weight: 800
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
